/**
 * Routes pour les statistiques
 */
const express = require('express');
const router = express.Router();
const db = require('../db');
const FoodService = require('../services/foodService');

/**
 * Récupère toutes les statistiques
 */
router.get('/all', async (req, res) => {
    try {
        // Récupérer l'état du jeu
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');

        // Récupérer les métiers
        const jobs = await db.all('SELECT * FROM jobs');

        // Récupérer les tables de modificateurs
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Calculer le modificateur de moral
        const moralModifier = gameState.moral_value - 1.0;

        // Calculer les statistiques de nourriture
        const foodGeneralModifier = modifierTables.find(m => m.id === 1)?.current_value || 0;
        const foodTechModifier = modifierTables.find(m => m.id === 2)?.current_value || 0;
        const foodProduction = FoodService.calculateFoodProduction(gameState, jobs, modifierTables, moralModifier);
        const foodConsumption = FoodService.calculateFoodConsumption(jobs);

        // Calculer les statistiques de moral
        const moralStats = {
            value: gameState.moral_value,
            modifier: moralModifier,
            title: gameState.moral_title
        };

        // Calculer les statistiques de santé
        const healthModifier = modifierTables.find(m => m.id === 6)?.current_value || 0;
        const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0) || 0;
        const totalSick = jobs.reduce((sum, job) => sum + (job.sick || 0), 0) || 0;
        const sickPercentage = totalInhabitants > 0 ? (totalSick / totalInhabitants) : 0;
        const baseProbability = 0.02; // 2% de base
        const adjustedProbability = Math.max(0, baseProbability - healthModifier - moralModifier);
        const sicknessProbability = Math.min(1, Math.max(0, adjustedProbability));

        const healthStats = {
            totalInhabitants,
            totalSick,
            sickPercentage,
            sicknessProbability,
            healthFactor: healthModifier,
            moralEffect: moralModifier
        };

        // Calculer les statistiques de recherche
        const researchModifier = modifierTables.find(m => m.id === 7)?.current_value || 0;
        const researchers = jobs.find(j => j.name === 'Researcher') || { number: 0, sick: 0 };
        const activeResearchers = researchers.number - researchers.sick;
        const researchPoints = activeResearchers * (1 + researchModifier + moralModifier);
        const nextLevelCost = 100 * Math.pow(gameState.tech_level + 1, 2);
        const cyclesRemaining = researchPoints > 0 ? Math.ceil(nextLevelCost / researchPoints) : Infinity;

        const researchStats = {
            activeResearchers,
            researchPoints,
            techLevel: gameState.tech_level,
            nextLevelCost,
            cyclesRemaining,
            researchFactor: researchModifier,
            moralEffect: moralModifier
        };

        // Retourner toutes les statistiques
        res.json({
            success: true,
            foodStats: {
                production: foodProduction,
                consumption: foodConsumption,
                net: foodProduction - foodConsumption,
                currentReserves: gameState.food_reserves,
                perishableLoss: gameState.food_reserves * (modifierTables.find(m => m.id === 3)?.current_value || 0.2),
                seasonFactor: gameState.season_factor,
                farmers: jobs.find(j => j.name === 'Farmer')?.number - jobs.find(j => j.name === 'Farmer')?.sick || 0,
                farmersSick: jobs.find(j => j.name === 'Farmer')?.sick || 0,
                totals: {
                    generalEffectsTotal: foodGeneralModifier,
                    techEffectsTotal: foodTechModifier,
                    perishableFactor: modifierTables.find(m => m.id === 3)?.current_value || 0.2,
                    totalProductionBonus: foodGeneralModifier + foodTechModifier
                }
            },
            moralStats,
            healthStats,
            researchStats
        });
    } catch (error) {
        console.error('Error getting all stats:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Individual stats endpoints for RTK Query
 */

// Food stats endpoint
router.get('/food', async (req, res) => {
    try {
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        const jobs = await db.all('SELECT * FROM jobs');
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        const foodData = await FoodService.getFoodData();

        res.json({
            success: true,
            ...foodData
        });
    } catch (error) {
        console.error('Error getting food stats:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Mining stats endpoint
router.get('/mining', async (req, res) => {
    try {
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        const jobs = await db.all('SELECT * FROM jobs');
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Calculate mining stats (you'll need to implement MiningService.getMiningData())
        res.json({
            success: true,
            message: 'Mining stats endpoint - implementation needed'
        });
    } catch (error) {
        console.error('Error getting mining stats:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Materials stats endpoint
router.get('/materials', async (req, res) => {
    try {
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        const jobs = await db.all('SELECT * FROM jobs');
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Calculate materials stats (you'll need to implement MaterialsService.getMaterialsData())
        res.json({
            success: true,
            message: 'Materials stats endpoint - implementation needed'
        });
    } catch (error) {
        console.error('Error getting materials stats:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Charges stats endpoint
router.get('/charges', async (req, res) => {
    try {
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        const jobs = await db.all('SELECT * FROM jobs');
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Calculate charges stats (you'll need to implement ChargesService.getChargesData())
        res.json({
            success: true,
            message: 'Charges stats endpoint - implementation needed'
        });
    } catch (error) {
        console.error('Error getting charges stats:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Health stats endpoint
router.get('/health', async (req, res) => {
    try {
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        const jobs = await db.all('SELECT * FROM jobs');
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Calculate health stats (you'll need to implement HealthService.getHealthData())
        res.json({
            success: true,
            message: 'Health stats endpoint - implementation needed'
        });
    } catch (error) {
        console.error('Error getting health stats:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Renown stats endpoint
router.get('/renown', async (req, res) => {
    try {
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        const jobs = await db.all('SELECT * FROM jobs');
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Calculate renown stats (you'll need to implement RenownService.getRenownData())
        res.json({
            success: true,
            message: 'Renown stats endpoint - implementation needed'
        });
    } catch (error) {
        console.error('Error getting renown stats:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
