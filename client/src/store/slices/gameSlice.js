import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { gameApi } from '../../services/api';
import cacheService from '../../services/CacheService';

// Variable pour suivre si une requête est en cours
let isFetchingGameState = false;
let lastFetchTime = 0;
const FETCH_COOLDOWN = 0; // Désactiver le cooldown temporairement

// Fonction pour réinitialiser l'état de la requête
export const resetFetchState = () => {
  console.log('resetFetchState: Réinitialisation de l\'état de la requête');
  isFetchingGameState = false;
  lastFetchTime = 0; // Réinitialiser également le temps de la dernière requête
};

// Async thunk for fetching game state
export const fetchGameState = createAsyncThunk(
  'game/fetchGameState',
  async (options = {}, { rejectWithValue, dispatch, getState }) => {
    const requestId = Date.now();
    const { force = false } = options;
    console.log(`[${requestId}] fetchGameState thunk: Starting with options`, { force });
    console.log(`[${requestId}] fetchGameState thunk: Stack trace:`, new Error().stack);

    // Vérifier si une requête est déjà en cours
    if (isFetchingGameState && !force) {
      console.log(`[${requestId}] fetchGameState thunk: Une requête est déjà en cours, ignorée`);
      return rejectWithValue({
        message: 'Une requête est déjà en cours',
        code: 'REQUEST_IN_PROGRESS',
        timestamp: Date.now()
      });
    }

    // Vérifier le cooldown
    const now = Date.now();
    if (!force && now - lastFetchTime < FETCH_COOLDOWN) {
      console.log(`[${requestId}] fetchGameState thunk: Cooldown actif (${Math.floor((now - lastFetchTime) / 1000)}s), ignorée`);
      return rejectWithValue({
        message: 'Cooldown actif',
        code: 'COOLDOWN_ACTIVE',
        timestamp: Date.now(),
        remainingTime: FETCH_COOLDOWN - (now - lastFetchTime)
      });
    }

    try {
      // Marquer comme en cours
      isFetchingGameState = true;
      lastFetchTime = now;

      console.log(`[${requestId}] fetchGameState thunk: Récupération des données du jeu`);

      // Fetch game state with timeout
      console.log(`[${requestId}] fetchGameState thunk: Appel de gameApi.getGameState()`);

      // Créer une promesse avec timeout
      const gameStatePromise = Promise.race([
        gameApi.getGameState(force),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout lors de la récupération des données du jeu')), 10000)
        )
      ]);

      // Attendre le résultat
      const gameState = await gameStatePromise;
      console.log(`[${requestId}] fetchGameState thunk: gameState récupéré`, gameState);

      // Vérifier que les données sont valides
      if (!gameState || (typeof gameState === 'object' && Object.keys(gameState).length === 0)) {
        throw new Error('Données du jeu invalides ou vides');
      }

      // Fetch events with timeout
      console.log(`[${requestId}] fetchGameState thunk: Appel de gameApi.getEvents()`);

      // Créer une promesse avec timeout
      const eventsPromise = Promise.race([
        gameApi.getEvents(force),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout lors de la récupération des événements')), 5000)
        )
      ]);

      // Attendre le résultat
      let events;
      try {
        events = await eventsPromise;
        console.log(`[${requestId}] fetchGameState thunk: events récupérés`, events);
      } catch (eventsError) {
        // Si la récupération des événements échoue, continuer avec un tableau vide
        console.warn(`[${requestId}] fetchGameState thunk: Erreur lors de la récupération des événements, utilisation d'un tableau vide`, eventsError);
        events = [];
      }

      // Combine data
      // Vérifier si les données sont dans le format attendu (avec gameState comme propriété)
      const result = gameState.gameState
        ? gameState // Si gameState est déjà un objet avec gameState comme propriété
        : {
            ...gameState,
            events: Array.isArray(events) ? events : []
          };

      console.log(`[${requestId}] fetchGameState thunk: Données combinées`, result);
      return result;
    } catch (error) {
      console.error(`[${requestId}] fetchGameState thunk: Erreur lors de la récupération des données`, error);

      // Créer un objet d'erreur détaillé
      const errorPayload = {
        message: error.message || 'Erreur inconnue lors de la récupération des données',
        code: error.code || 'UNKNOWN_ERROR',
        timestamp: Date.now(),
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined
      };

      return rejectWithValue(errorPayload);
    } finally {
      // Marquer comme terminé
      isFetchingGameState = false;
      console.log(`[${requestId}] fetchGameState thunk: Terminé`);
    }
  }
);

// Async thunk for processing next cycle
export const processNextCycle = createAsyncThunk(
  'game/processNextCycle',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      const data = await gameApi.processNextCycle();

      if (!data.success) {
        throw new Error(data.error || 'Failed to process next cycle');
      }

      return data;
    } catch (error) {
      console.error('Error processing next cycle:', error);

      // Assurer que l'état loading est réinitialisé en cas d'erreur
      return rejectWithValue(error.message || 'Une erreur est survenue lors du traitement du cycle');
    }
  }
);

// Async thunk for transferring jobs
export const transferJob = createAsyncThunk(
  'game/transferJob',
  async ({ fromJobId, toJobId, count }, { rejectWithValue }) => {
    try {
      const data = await gameApi.transferJob(fromJobId, toJobId, count);

      if (!data.success) {
        throw new Error(data.error || 'Failed to transfer job');
      }

      return data.jobs || [];
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Async thunk for changing free workers
export const changeFreeWorkers = createAsyncThunk(
  'game/changeFreeWorkers',
  async ({ jobId, change }, { rejectWithValue }) => {
    try {
      const data = await gameApi.changeFreeWorkers(jobId, change);

      if (!data.success) {
        throw new Error(data.error || 'Failed to change free workers');
      }

      return data.jobs || [];
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Initial state
const initialState = {
  // Données principales du jeu
  gameState: null,

  // Données extraites pour un accès plus facile
  // Ces données sont également présentes dans gameState mais sont dupliquées ici
  // pour faciliter l'accès via les sélecteurs
  modifierTables: [],
  events: [],
  jobs: [],
  foodData: null,
  materialsData: null,
  miningData: null,

  // État de l'UI
  loading: false, // Assurons-nous que loading est initialisé à false
  error: null,
  cycleResults: null,

  // Métadonnées
  lastUpdated: null,
  lastFetchAttempt: null,
  fetchCount: 0
};

// Create the game slice
const gameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    clearCycleResults: (state) => {
      state.cycleResults = null;
    },
    forceStopLoading: (state) => {
      console.log('forceStopLoading: Réinitialisation de l\'état loading à false');
      state.loading = false;
    },
    resetLoadingState: (state) => {
      console.log('resetLoadingState: Réinitialisation de l\'état loading à false');
      state.loading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchGameState
      .addCase(fetchGameState.pending, (state) => {
        console.log('gameSlice: fetchGameState.pending');
        state.loading = true;
        state.error = null;
        state.lastFetchAttempt = Date.now();
        state.fetchCount++;
      })
      .addCase(fetchGameState.fulfilled, (state, action) => {
        console.log('gameSlice: fetchGameState.fulfilled', action.payload);
        state.loading = false;
        state.lastUpdated = Date.now();
        state.error = null;

        // Vérifier que les données sont valides
        if (!action.payload) {
          console.error('fetchGameState.fulfilled: payload is null or undefined');
          state.error = 'Données invalides reçues du serveur';
          return;
        }

        try {
          // Vérifier si les données sont dans le format attendu (avec gameState comme propriété)
          if (action.payload.gameState) {
            // Format avec gameState comme propriété
            state.gameState = action.payload.gameState;
            state.jobs = Array.isArray(action.payload.jobs) ? action.payload.jobs : [];
            state.events = Array.isArray(action.payload.events) ? action.payload.events : [];
            state.modifierTables = Array.isArray(action.payload.modifierTables) ? action.payload.modifierTables : [];
            state.foodData = action.payload.foodData || {};
            state.materialsData = action.payload.materialsData || {};
            state.miningData = action.payload.miningData || {};
          } else {
            // Format avec les données à la racine
            state.gameState = action.payload;
            state.jobs = Array.isArray(action.payload.jobs) ? action.payload.jobs : [];
            state.events = Array.isArray(action.payload.events) ? action.payload.events : [];
            state.modifierTables = Array.isArray(action.payload.modifierTables) ? action.payload.modifierTables : [];
            state.foodData = action.payload.foodData || {};
            state.materialsData = action.payload.materialsData || {};
            state.miningData = action.payload.miningData || {};
          }

          console.log('gameSlice: Données mises à jour avec succès', {
            gameState: state.gameState ? 'OK' : 'Manquant',
            events: state.events.length,
            jobs: state.jobs.length,
            modifierTables: state.modifierTables.length,
            foodData: state.foodData ? 'OK' : 'Manquant'
          });
        } catch (error) {
          console.error('gameSlice: Erreur lors du traitement des données', error);
          state.error = `Erreur lors du traitement des données: ${error.message}`;
        }
      })
      .addCase(fetchGameState.rejected, (state, action) => {
        console.log('gameSlice: fetchGameState.rejected', action.payload);

        // TOUJOURS réinitialiser l'état de chargement, même en cas de cooldown ou de requête en cours
        state.loading = false;

        // Ne pas mettre à jour l'état d'erreur si l'erreur est due au cooldown ou à une requête déjà en cours
        if (action.payload && (
            action.payload.code === 'COOLDOWN_ACTIVE' ||
            action.payload.code === 'REQUEST_IN_PROGRESS'
        )) {
          console.log('gameSlice: Ignoring error due to cooldown or in-progress request');
          return;
        }

        // Stocker l'erreur de manière structurée
        if (action.payload && typeof action.payload === 'object') {
          state.error = action.payload;
        } else {
          state.error = {
            message: action.payload || 'Erreur inconnue lors de la récupération des données',
            code: 'UNKNOWN_ERROR',
            timestamp: Date.now()
          };
        }

        // Incrémenter le compteur d'erreurs pour le suivi
        state.fetchCount = (state.fetchCount || 0) + 1;
      })
      // Handle processNextCycle
      .addCase(processNextCycle.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(processNextCycle.fulfilled, (state, action) => {
        state.loading = false;
        state.gameState = action.payload.gameState;

        // Extraire les données importantes au niveau racine
        if (Array.isArray(action.payload.gameState.jobs)) {
          state.jobs = action.payload.gameState.jobs;
        }

        // Stocker les résultats du cycle
        state.cycleResults = {
          events: action.payload.currentCycleEvents,
          calculations: action.payload.calculations
        };

        // Mettre à jour les données de nourriture
        if (action.payload.foodData) {
          state.foodData = action.payload.foodData;
        }
      })
      .addCase(processNextCycle.rejected, (state, action) => {
        // Assurer que l'état loading est réinitialisé en cas d'erreur
        state.loading = false;

        // Stocker l'erreur de manière structurée
        if (action.payload && typeof action.payload === 'object') {
          state.error = action.payload;
        } else {
          state.error = {
            message: action.payload || 'Erreur inconnue lors du traitement du cycle',
            code: 'CYCLE_ERROR',
            timestamp: Date.now()
          };
        }

        console.log('processNextCycle.rejected: État loading réinitialisé à false');
      })
      // Handle transferJob
      .addCase(transferJob.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(transferJob.fulfilled, (state, action) => {
        state.loading = false;
        if (state.gameState) {
          state.gameState.jobs = action.payload;
        }
      })
      .addCase(transferJob.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Handle changeFreeWorkers
      .addCase(changeFreeWorkers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(changeFreeWorkers.fulfilled, (state, action) => {
        state.loading = false;
        if (state.gameState) {
          state.gameState.jobs = action.payload;
        }
      })
      .addCase(changeFreeWorkers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

// Export actions
export const { clearCycleResults, forceStopLoading, resetLoadingState } = gameSlice.actions;

// Action pour forcer l'arrêt du chargement et réinitialiser l'état d'erreur
export const resetGameState = () => (dispatch) => {
  console.log('resetGameState: Réinitialisation de l\'état du jeu');

  // Forcer l'arrêt du chargement
  dispatch(forceStopLoading());

  // Invalider le cache
  cacheService.invalidateAll();

  // Ne pas recharger automatiquement les données pour éviter les boucles
  console.log('resetGameState: Le rechargement automatique a été désactivé. Utilisez le bouton "Charger les données" pour recharger manuellement.');
};

// Sélecteurs de base - non mémorisés, utilisés comme dépendances pour les sélecteurs mémorisés
const selectGameStateRaw = (state) => state.game.gameState;
const selectModifierTablesRaw = (state) => state.game.modifierTables || [];
const selectEventsRaw = (state) => state.game.events || [];
const selectJobsRaw = (state) => state.game.jobs || [];
const selectGameErrorRaw = (state) => state.game.error;
const selectCycleResultsRaw = (state) => state.game.cycleResults;

// Sélecteurs de base - pas besoin de mémoisation pour les sélecteurs simples
export const selectGameState = selectGameStateRaw;
export const selectModifierTables = selectModifierTablesRaw;
export const selectEvents = selectEventsRaw;
export const selectJobs = selectJobsRaw;
export const selectGameError = selectGameErrorRaw;
export const selectCycleResults = selectCycleResultsRaw;

// Sélecteurs avec transformation - only use createSelector when actual transformation is needed
export const selectGameLoading = (state) => {
  const loading = state.game.loading;
  console.log('selectGameLoading appelé, valeur:', loading);
  return loading;
};

export const selectFoodData = (state) => state.game.foodData || {};

export const selectMaterialsData = (state) => state.game.materialsData || {};

export const selectMiningData = (state) => state.game.miningData || {};

// Sélecteurs dérivés pour des données spécifiques - utiliser des sélecteurs simples avec valeurs par défaut
export const selectGameCycle = (state) => state.game.gameState?.cycle_number || 0;
export const selectGameMonth = (state) => state.game.gameState?.month || 'Rubis';
export const selectGameYear = (state) => state.game.gameState?.year || 1326;
export const selectSeasonFactor = (state) => state.game.gameState?.season_factor || 1.0;
export const selectTreasure = (state) => state.game.gameState?.treasure || 0;
export const selectFoodReserves = (state) => state.game.gameState?.food_reserves || 0;
export const selectMaterials = (state) => state.game.gameState?.materials || 0;
export const selectMoralValue = (state) => state.game.gameState?.moral_value || 1.0;

// Sélecteur pour obtenir un modificateur par ID de table
export const selectModifierTableById = (tableId) => createSelector(
  [selectModifierTables],
  (modifierTables) => modifierTables.find(table => table.id === tableId) || { id: tableId, current_value: 0 }
);

// Export reducer
export default gameSlice.reducer;
