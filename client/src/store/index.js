import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import gameReducer from './slices/gameSlice';
import jobsReducer from './slices/jobsSlice';
import resourcesReducer from './slices/resourcesSlice';
import uiReducer from './slices/uiSlice';
import miningReducer from './slices/miningSlice';
import materialsReducer from './slices/materialsSlice';
import chargesReducer from './slices/chargesSlice';
import foodReducer from './slices/foodSlice';
import moralReducer from './slices/moralSlice';
import healthReducer from './slices/healthSlice';
import researchReducer from './slices/researchSlice';
import tradingReducer from './slices/tradingSlice';
import justiceDefenseReducer from './slices/justiceDefenseSlice';
import renownReducer from './slices/renownSlice';
import populationReducer from './slices/populationSlice';
import websocketReducer from './slices/websocketSlice';
import entitiesReducer from './slices/entitiesSlice';
import eventsReducer from './slices/eventsSlice';
import calendarReducer from './slices/calendarSlice';
import buildingsReducer from './slices/buildingsSlice';
import websocketMiddleware from '../middleware/websocketMiddleware';
// Import RTK Query API and enhanced WebSocket middleware
import { gameApi } from './api/gameApi';
import websocketRTKMiddleware from '../middleware/websocketRTKMiddleware';

// Configure the Redux store
export const store = configureStore({
  reducer: {
    // RTK Query API reducer
    [gameApi.reducerPath]: gameApi.reducer,
    // Existing reducers (will be gradually migrated to RTK Query)
    game: gameReducer,
    jobs: jobsReducer,
    resources: resourcesReducer,
    ui: uiReducer,
    mining: miningReducer,
    materials: materialsReducer,
    charges: chargesReducer,
    food: foodReducer,
    moral: moralReducer,
    health: healthReducer,
    research: researchReducer,
    trading: tradingReducer,
    justiceDefense: justiceDefenseReducer,
    renown: renownReducer,
    population: populationReducer,
    websocket: websocketReducer,
    entities: entitiesReducer,
    events: eventsReducer,
    calendar: calendarReducer,
    buildings: buildingsReducer,
  },
  // Enable Redux DevTools
  devTools: process.env.NODE_ENV !== 'production',
  // Add middleware for RTK Query and enhanced WebSocket
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['websocket.socket']
      }
    })
    .concat(gameApi.middleware)
    .concat(websocketRTKMiddleware())
    .concat(websocketMiddleware()) // Keep old middleware for compatibility during migration
});

// Setup listeners for RTK Query to enable automatic refetching
setupListeners(store.dispatch);

// Export the store and RTK Query API for use in components
export { gameApi };
export default store;
