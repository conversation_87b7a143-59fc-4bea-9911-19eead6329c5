import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Base query with error handling
const baseQuery = fetchBaseQuery({
  baseUrl: 'http://localhost:3001/api/',
  prepareHeaders: (headers) => {
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

// Enhanced base query with error handling
const baseQueryWithErrorHandling = async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions);

  if (result.error) {
    console.error('API Error:', result.error);
  }

  return result;
};

// Create the main API slice
export const gameApi = createApi({
  reducerPath: 'gameApi',
  baseQuery: baseQueryWithErrorHandling,
  tagTypes: [
    'GameState',
    'Jobs',
    'Events',
    'Modifiers',
    'Inhabitants',
    'Buildings',
    'Stats',
    'Dashboard'
  ],
  endpoints: (builder) => ({
    // Game State endpoints
    getGameState: builder.query({
      query: () => 'game/state',
      providesTags: ['GameState'],
    }),

    getDashboard: builder.query({
      query: () => 'game/dashboard',
      providesTags: ['Dashboard', 'GameState', 'Jobs'],
    }),

    processNextCycle: builder.mutation({
      query: () => ({
        url: 'game/next-cycle',
        method: 'POST',
      }),
      invalidatesTags: ['GameState', 'Jobs', 'Events', 'Stats', 'Dashboard'],
    }),

    // Jobs endpoints
    getJobs: builder.query({
      query: () => 'game/jobs',
      providesTags: ['Jobs'],
    }),

    changeJobNumber: builder.mutation({
      query: ({ jobId, change }) => ({
        url: 'game/change-job',
        method: 'POST',
        body: { jobId, change },
      }),
      invalidatesTags: ['Jobs', 'GameState', 'Stats', 'Dashboard'],
    }),

    changeWorkerNumber: builder.mutation({
      query: ({ change }) => ({
        url: 'game/change-workers',
        method: 'POST',
        body: { change },
      }),
      invalidatesTags: ['Jobs', 'GameState', 'Stats', 'Dashboard'],
    }),

    changeFreeWorkers: builder.mutation({
      query: ({ jobId, change }) => ({
        url: 'game/change-free-workers',
        method: 'POST',
        body: { jobId, change },
      }),
      invalidatesTags: ['Jobs', 'GameState', 'Stats', 'Dashboard'],
    }),

    transferJob: builder.mutation({
      query: ({ fromJobId, toJobId, count }) => ({
        url: 'job-transfer/transfer',
        method: 'POST',
        body: { fromJobId, toJobId, count },
      }),
      invalidatesTags: ['Jobs', 'GameState', 'Stats', 'Dashboard'],
    }),

    // Events endpoints
    getEvents: builder.query({
      query: () => 'game/events',
      providesTags: ['Events'],
    }),

    // Modifiers endpoints
    getModifiersByTable: builder.query({
      query: (tableId) => `modifiers/table/${tableId}`,
      providesTags: (result, error, tableId) => [{ type: 'Modifiers', id: tableId }],
    }),

    addModifier: builder.mutation({
      query: ({ tableId, modifier }) => ({
        url: `modifiers/table/${tableId}`,
        method: 'POST',
        body: modifier,
      }),
      invalidatesTags: (result, error, { tableId }) => [
        { type: 'Modifiers', id: tableId },
        'Stats',
        'Dashboard'
      ],
    }),

    updateModifier: builder.mutation({
      query: ({ id, modifier }) => ({
        url: `modifiers/${id}`,
        method: 'PUT',
        body: modifier,
      }),
      invalidatesTags: ['Modifiers', 'Stats', 'Dashboard'],
    }),

    deleteModifier: builder.mutation({
      query: (id) => ({
        url: `modifiers/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Modifiers', 'Stats', 'Dashboard'],
    }),

    // Stats endpoints
    getMiningStats: builder.query({
      query: () => 'stats/mining',
      providesTags: ['Stats'],
    }),

    getFoodStats: builder.query({
      query: () => 'stats/food',
      providesTags: ['Stats'],
    }),

    getMaterialsStats: builder.query({
      query: () => 'stats/materials',
      providesTags: ['Stats'],
    }),

    getChargesStats: builder.query({
      query: () => 'stats/charges',
      providesTags: ['Stats'],
    }),

    getTradingStats: builder.query({
      query: () => 'stats/trading',
      providesTags: ['Stats'],
    }),

    getHealthStats: builder.query({
      query: () => 'stats/health',
      providesTags: ['Stats'],
    }),

    getRenownStats: builder.query({
      query: () => 'stats/renown',
      providesTags: ['Stats'],
    }),

    getJusticeDefenseStats: builder.query({
      query: () => 'stats/justice-defense',
      providesTags: ['Stats'],
    }),

    // Inhabitants endpoints
    getInhabitants: builder.query({
      query: (params = {}) => ({
        url: 'inhabitants',
        params,
      }),
      providesTags: ['Inhabitants'],
    }),

    getInhabitant: builder.query({
      query: (id) => `inhabitants/${id}`,
      providesTags: (result, error, id) => [{ type: 'Inhabitants', id }],
    }),

    createInhabitant: builder.mutation({
      query: (inhabitant) => ({
        url: 'inhabitants',
        method: 'POST',
        body: inhabitant,
      }),
      invalidatesTags: ['Inhabitants', 'Stats', 'Dashboard'],
    }),

    updateInhabitant: builder.mutation({
      query: ({ id, inhabitant }) => ({
        url: `inhabitants/${id}`,
        method: 'PUT',
        body: inhabitant,
      }),
      invalidatesTags: ['Inhabitants', 'Stats', 'Dashboard'],
    }),

    deleteInhabitant: builder.mutation({
      query: (id) => ({
        url: `inhabitants/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Inhabitants', 'Stats', 'Dashboard'],
    }),

    syncInhabitantsWithJobs: builder.mutation({
      query: () => ({
        url: 'inhabitants/sync',
        method: 'POST',
      }),
      invalidatesTags: ['Inhabitants', 'Jobs', 'Stats', 'Dashboard'],
    }),

    getPopulationStats: builder.query({
      query: () => 'inhabitants/stats',
      providesTags: ['Stats'],
    }),

    // Buildings endpoints
    getBuildings: builder.query({
      query: () => 'buildings',
      providesTags: ['Buildings'],
    }),

    getBuildingPlans: builder.query({
      query: () => 'buildings/plans',
      providesTags: ['Buildings'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Game State hooks
  useGetGameStateQuery,
  useGetDashboardQuery,
  useProcessNextCycleMutation,

  // Jobs hooks
  useGetJobsQuery,
  useChangeJobNumberMutation,
  useChangeWorkerNumberMutation,
  useChangeFreeWorkersMutation,
  useTransferJobMutation,

  // Events hooks
  useGetEventsQuery,

  // Modifiers hooks
  useGetModifiersByTableQuery,
  useAddModifierMutation,
  useUpdateModifierMutation,
  useDeleteModifierMutation,

  // Stats hooks
  useGetMiningStatsQuery,
  useGetFoodStatsQuery,
  useGetMaterialsStatsQuery,
  useGetChargesStatsQuery,
  useGetTradingStatsQuery,
  useGetHealthStatsQuery,
  useGetRenownStatsQuery,
  useGetJusticeDefenseStatsQuery,

  // Inhabitants hooks
  useGetInhabitantsQuery,
  useGetInhabitantQuery,
  useCreateInhabitantMutation,
  useUpdateInhabitantMutation,
  useDeleteInhabitantMutation,
  useSyncInhabitantsWithJobsMutation,
  useGetPopulationStatsQuery,

  // Buildings hooks
  useGetBuildingsQuery,
  useGetBuildingPlansQuery,
} = gameApi;

export default gameApi;;
