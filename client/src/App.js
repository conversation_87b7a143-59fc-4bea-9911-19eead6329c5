import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import './App.css';
import CycleResultsModal from './components/CycleResultsModal';
import { wsConnect, wsDisconnect } from './middleware/websocketMiddleware';
import { selectWebSocketConnected, selectWebSocketError } from './store/slices/websocketSlice';


// Redux actions and selectors
import {
  fetchGameState,
  processNextCycle,
  resetLoadingState,
  resetFetchState,

  selectGameState,
  selectGameLoading,
  selectJobs,
  selectEvents,
  selectFoodData,
  selectMaterialsData,
  selectGameError
} from './store/slices/gameSlice';
import {
  selectTradingStats
} from './store/slices/tradingSlice';
import {
  selectMiningStats
} from './store/slices/miningSlice';
import {
  selectChargesStats
} from './store/slices/chargesSlice';
import {
  setActiveTab,
  setShowCycleModal,
  selectActiveTab,
  selectShowCycleModal,
  incrementMoralUpdated,
  incrementHealthUpdated,
  incrementResearchUpdated,
  incrementTradingUpdated,
  incrementJusticeDefenseUpdated
} from './store/slices/uiSlice';
// Pas d'imports de sélecteurs Redux pour éviter les boucles infinies

// Component imports
import FoodTabRedux from './components/FoodTabRedux';
import MoralTabRedux from './components/MoralTabRedux';
import MiningTabRedux from './components/MiningTabRedux';
import MaterialsTabRedux from './components/MaterialsTabRedux';
import ChargesTabRedux from './components/ChargesTabRedux';
import RenownTabRedux from './components/RenownTabRedux';
import HealthTabRedux from './components/HealthTabRedux';
import ResearchTabRedux from './components/ResearchTabRedux';
import TradingTabRedux from './components/TradingTabRedux';
import JusticeDefenseTabRedux from './components/JusticeDefenseTabRedux';
import PopulationTabRedux from './components/PopulationTabRedux';
import EventsTabRedux from './components/EventsTabRedux';
import CalendarTabRedux from './components/CalendarTabRedux';
import BuildingsTabRedux from './components/BuildingsTabRedux';
import ConstructionTabRedux from './components/ConstructionTabRedux';
import BuildingPlansTabRedux from './components/BuildingPlansTabRedux';
import WebSocketStatus from './components/WebSocketStatus';

// Example components have been removed

function App() {
  const dispatch = useDispatch();

  // WebSocket state
  const isConnected = useSelector(selectWebSocketConnected);
  const wsError = useSelector(selectWebSocketError);

  // Game state
  const gameState = useSelector(selectGameState);
  const jobs = useSelector(selectJobs);
  const events = useSelector(selectEvents);
  const foodData = useSelector(selectFoodData);
  const materialsData = useSelector(selectMaterialsData);
  const tradingStats = useSelector(selectTradingStats);
  const miningStats = useSelector(selectMiningStats);
  const chargesStats = useSelector(selectChargesStats);
  const loading = useSelector(selectGameLoading);
  const error = useSelector(selectGameError);

  // Afficher l'état de loading pour le débogage
  console.log('App: État de loading:', loading);

  // Données financières - EXACTEMENT comme foodData et materialsData
  const currentTreasure = gameState?.treasure || 0;

  // Utiliser les données des slices spécialisés (comme foodData et materialsData)
  const miningRevenue = miningStats?.production || 0;
  const tradingRevenue = tradingStats?.tradingRevenue || 0;
  const totalRevenue = miningRevenue + tradingRevenue;

  const salaries = chargesStats?.salaries || 0;
  const nonSalaryCharges = chargesStats?.nonSalaryCharges || 0;
  const totalExpenses = salaries + nonSalaryCharges;

  const netFinances = totalRevenue - totalExpenses;





  // Surveiller les changements de l'état loading
  useEffect(() => {
    console.log('App: État de loading a changé:', loading);
  }, [loading]);







  // UI state
  // Si un onglet est sauvegardé dans le localStorage, l'utiliser et le supprimer
  useEffect(() => {
    const savedTab = localStorage.getItem('activeTab');
    if (savedTab) {
      console.log(`App: Restauration de l'onglet ${savedTab} depuis le localStorage`);
      dispatch(setActiveTab(savedTab));
      // Supprimer l'onglet du localStorage pour ne pas le restaurer à chaque rechargement
      localStorage.removeItem('activeTab');
    }
  }, [dispatch]);

  const activeTab = useSelector(selectActiveTab);
  const showCycleModal = useSelector(selectShowCycleModal);

  // Fonction pour obtenir l'emoji en fonction du moral
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    // Utiliser des comparaisons exactes pour éviter les problèmes
    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐'; // Valeur par défaut
  };

  // Charger l'état du jeu au démarrage - VERSION SIMPLIFIÉE
  useEffect(() => {
    console.log('App: Chargement initial de l\'état du jeu');
    // Réinitialiser l'état de loading avant de charger les données
    dispatch(resetLoadingState());
    // Réinitialiser l'état de la requête
    resetFetchState();
    // Forcer le chargement des données
    dispatch(fetchGameState({ force: true }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Pas de dépendances pour éviter les boucles

  // SUPPRIMÉ - Ce useEffect causait des boucles infinies
  // Les données sont chargées par les composants individuels quand nécessaire




  // Establish WebSocket connection on component mount
  useEffect(() => {
    // Build WebSocket URL
    // Use the same port as the API server since we've integrated the WebSocket server
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    console.log('App: Connecting to WebSocket server at', wsUrl);

    // Add a small delay to ensure the server is ready
    const timeoutId = setTimeout(() => {
      // Dispatch action to connect
      dispatch(wsConnect(wsUrl));
    }, 1000);

    // Clean up connection when component unmounts
    return () => {
      clearTimeout(timeoutId);
      console.log('App: Disconnecting from WebSocket server');
      dispatch(wsDisconnect());
    };
  }, [dispatch]);

  // Log WebSocket connection status
  useEffect(() => {
    console.log('App: WebSocket connection status:', isConnected ? 'Connected' : 'Disconnected');

    if (wsError) {
      console.error('App: WebSocket error:', wsError);
    }
  }, [isConnected, wsError]);

  // Refetch game state when moral, health, research, trading, or justice/defense is updated - DÉSACTIVÉ POUR ÉVITER LES RECHARGEMENTS AUTOMATIQUES
  // But only once per update, then reset the counters
  // useEffect(() => {
  //   // Utiliser un debounce pour éviter les appels trop fréquents
  //   const shouldUpdate = moralUpdated > 0 || healthUpdated > 0 || researchUpdated > 0 || tradingUpdated > 0 || justiceDefenseUpdated > 0;

  //   if (shouldUpdate) {
  //     console.log('App: Mise à jour de l\'état du jeu en raison de modifications');

  //     // Utiliser un timeout pour regrouper les mises à jour
  //     const timeoutId = setTimeout(() => {
  //       console.log('App: Dispatching fetchGameState');
  //       dispatch(fetchGameState());

  //       // Reset all counters to prevent infinite loops
  //       if (moralUpdated > 0) dispatch(resetMoralUpdated());
  //       if (healthUpdated > 0) dispatch(resetHealthUpdated());
  //       if (researchUpdated > 0) dispatch(resetResearchUpdated());
  //       if (tradingUpdated > 0) dispatch(resetTradingUpdated());
  //       if (justiceDefenseUpdated > 0) dispatch(resetJusticeDefenseUpdated());
  //     }, 500); // Attendre 500ms avant de déclencher la mise à jour

  //     // Nettoyer le timeout si le composant est démonté ou si les dépendances changent
  //     return () => clearTimeout(timeoutId);
  //   }
  // }, [dispatch, moralUpdated, healthUpdated, researchUpdated, tradingUpdated, justiceDefenseUpdated]);

  // Handle keyboard navigation for tabs
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Get all tab IDs in order
      const tabIds = [
        'dashboard', 'events', 'calendar', 'population', 'food', 'mining', 'charges',
        'moral', 'materials', 'health', 'construction', 'research', 'renown',
        'trading', 'justice', 'buildings', 'building-plans'
      ];

      // Find current tab index
      const currentIndex = tabIds.indexOf(activeTab);

      if (e.key === 'ArrowUp') {
        // Go to previous tab, or wrap to last tab
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : tabIds.length - 1;
        const newTab = tabIds[prevIndex];
        dispatch(setActiveTab(newTab));
      } else if (e.key === 'ArrowDown') {
        // Go to next tab, or wrap to first tab
        const nextIndex = currentIndex < tabIds.length - 1 ? currentIndex + 1 : 0;
        const newTab = tabIds[nextIndex];
        dispatch(setActiveTab(newTab));
      }
    };

    // Add event listener
    window.addEventListener('keydown', handleKeyDown);

    // Clean up
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [activeTab, dispatch]);

  // Handle next cycle button click
  const handleNextCycle = async () => {
    try {
      // Réinitialiser l'état de loading avant d'exécuter le cycle
      dispatch(resetLoadingState());
      // Réinitialiser l'état de la requête
      resetFetchState();

      await dispatch(processNextCycle()).unwrap();
      console.log('Cycle exécuté avec succès');
    } catch (error) {
      console.error('Erreur lors de l\'exécution du cycle:', error);
      // Réinitialiser l'état de loading en cas d'erreur
      dispatch(resetLoadingState());
      resetFetchState();
    }
  };

  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    // Format with 1 decimal place
    return parseFloat(num).toFixed(1).replace(/\.0$/, '.0').toLocaleString();
  };



  // Afficher un message d'erreur
  if (error) {
    const errorMessage = typeof error === 'object' ? error.message || 'Erreur inconnue' : error;
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div style={{ fontSize: '24px', color: 'red' }}>
          Erreur de chargement: {errorMessage}
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      {/* WebSocket Status Indicator */}
      <WebSocketStatus />

      {/* Cycle Results Modal */}
      <CycleResultsModal
        isOpen={showCycleModal}
        onClose={() => dispatch(setShowCycleModal(false))}
      />

      {/* Sidebar with tabs */}
      <div className="sidebar">
        <div className="sidebar-header">
          <h3>Mine Simulator</h3>
        </div>
        <ul className="sidebar-tabs">
          <li
            className={`sidebar-tab ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('dashboard'));
            }}
          >
            <span className="sidebar-tab-icon">📊</span>
            Dashboard
          </li>
          {/* L'onglet Redux Demo a été supprimé car toute l'application utilise maintenant Redux */}
          {/* Tab Emplois supprimée */}
          <li
            className={`sidebar-tab ${activeTab === 'events' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('events'));
            }}
          >
            <span className="sidebar-tab-icon">📜</span>
            Journal
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'calendar' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('calendar'))}
          >
            <span className="sidebar-tab-icon">📅</span>
            Calendrier
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'population' ? 'active' : ''}`}
            onClick={() => {
              dispatch(setActiveTab('population'));
            }}
          >
            <span className="sidebar-tab-icon">👥</span>
            Population
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'food' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('food'))}
          >
            <span className="sidebar-tab-icon">🍎</span>
            Nourriture
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'mining' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('mining'))}
          >
            <span className="sidebar-tab-icon">⛏️</span>
            Minage
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'charges' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('charges'))}
          >
            <span className="sidebar-tab-icon">📊</span>
            Charges
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'moral' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('moral'))}
          >
            <span className="sidebar-tab-icon">{getMoralEmoji(gameState?.moral_title)}</span>
            Moral
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'materials' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('materials'))}
          >
            <span className="sidebar-tab-icon">🧱</span>
            Matériaux
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'health' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('health'))}
          >
            <span className="sidebar-tab-icon">🏥</span>
            Santé
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'construction' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('construction'))}
          >
            <span className="sidebar-tab-icon">🏗️</span>
            Construction
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'research' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('research'))}
          >
            <span className="sidebar-tab-icon">🔬</span>
            Recherche
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'renown' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('renown'))}
          >
            <span className="sidebar-tab-icon">🏆</span>
            Renommée
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'trading' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('trading'))}
          >
            <span className="sidebar-tab-icon">🔄</span>
            Commerce
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'justice' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('justice'))}
          >
            <span className="sidebar-tab-icon">⚖️</span>
            Justice et défense
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'buildings' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('buildings'))}
          >
            <span className="sidebar-tab-icon">🏢</span>
            Liste des bâtiments
          </li>
          <li
            className={`sidebar-tab ${activeTab === 'building-plans' ? 'active' : ''}`}
            onClick={() => dispatch(setActiveTab('building-plans'))}
          >
            <span className="sidebar-tab-icon">📝</span>
            Plans des bâtiments
          </li>

        </ul>
      </div>

      {/* Main content area */}
      <div className="main-content">
        {activeTab === 'dashboard' ? (
          <header className="App-header">
            <div className="header-container">
              <h1>Gestion de la Mine</h1>
              <div>
                <button
                  className="next-cycle-btn"
                  onClick={handleNextCycle}
                  disabled={loading}
                >
                  Exécuter un cycle
                </button>
              </div>
            </div>

            {!gameState && (
              <div style={{
                margin: '10px 0',
                padding: '10px',
                backgroundColor: '#ffcdd2',
                borderRadius: '4px',
                textAlign: 'center',
                fontWeight: 'bold'
              }}>
                ATTENTION: Aucune donnée n'est disponible. Le dashboard peut afficher des informations incorrectes ou incomplètes.
              </div>
            )}
          </header>
        ) : null}

        {/* Dashboard Tab */}
        <div className={`tab-content ${activeTab === 'dashboard' ? 'active' : ''}`}>
          {/* Game Info Section */}
          <div className="game-info">
            <div className="game-info-item">
              <div className="game-info-label">Cycle:</div>
              <div className="game-info-value">{gameState?.cycle_number || 'N/A'}</div>
            </div>
            <div className="game-info-item">
              <div className="game-info-label">Mois:</div>
              <div className="game-info-value">{gameState?.month || 'N/A'}</div>
            </div>
            <div className="game-info-item">
              <div className="game-info-label">Année:</div>
              <div className="game-info-value">{gameState?.year || 'N/A'}</div>
            </div>
            <div className="game-info-item">
              <div className="game-info-label">Saison:</div>
              <div className="game-info-value">
                {gameState?.season || 'N/A'} ({gameState?.season_factor ? (gameState.season_factor * 100) : 0}%)
              </div>
            </div>
          </div>



          {/* Recent Events */}
          {events && events.length > 0 && (
            <div className="section">
              <h2 className="section-title">Événements Récents</h2>
              <div className="events-container">
                {events.slice(0, 3).map((event, index) => (
                  <div key={index} className={`event-card ${event.event_type?.toLowerCase() || 'default'}`}>
                    <div className="event-icon">
                      {event.event_type === 'SICKNESS' && '🤒'}
                      {event.event_type === 'CRIME' && '🔪'}
                      {event.event_type === 'RESEARCH' && '💡'}
                      {event.event_type === 'FAMINE' && '🍽️'}
                      {event.event_type === 'TREASURY' && '💰'}
                    </div>
                    <div className="event-content">
                      <div className="event-cycle">Cycle {event.cycle}</div>
                      <div className="event-description">{event.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Resources Grid */}
          <div className="section">
            <h2 className="section-title">Ressources</h2>
            <div className="resources-grid">
              {/* Treasury Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Finances</div>
                  <div className="resource-icon">💰</div>
                </div>
                <div className="resource-value">
                  {formatNumber(currentTreasure)}
                  <span className="resource-unit"> or</span>
                </div>
                <div className={`resource-change ${netFinances >= 0 ? 'positive' : 'negative'}`}>
                  {netFinances >= 0 ? '+' : ''}{formatNumber(netFinances)} or/cycle
                </div>
                <div className="resource-details">
                  <div className="resource-detail-item">
                    <span>Revenus miniers:</span>
                    <span className="positive">+{formatNumber(miningRevenue)}</span>
                  </div>
                  <div className="resource-detail-item">
                    <span>Revenus commerciaux:</span>
                    <span className="positive">+{formatNumber(tradingRevenue)}</span>
                  </div>
                  <div className="resource-detail-item">
                    <span>Charges non salariales:</span>
                    <span className="negative">-{formatNumber(nonSalaryCharges)}</span>
                  </div>
                  <div className="resource-detail-item">
                    <span>Salaires:</span>
                    <span className="negative">-{formatNumber(salaries)}</span>
                  </div>
                </div>
              </div>

              {/* Materials Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Matériaux</div>
                  <div className="resource-icon">🧱</div>
                </div>
                <div className="resource-value">
                  {formatNumber(gameState?.materials)}
                  <span className="resource-unit"> unités</span>
                </div>
                <div className="resource-change positive">
                  +{formatNumber(materialsData?.production || 0)} unités/cycle
                </div>
              </div>

              {/* Food Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Vivres</div>
                  <div className="resource-icon">🍎</div>
                </div>
                <div className="resource-value">
                  {formatNumber(gameState?.food_reserves)}
                  <span className="resource-unit"> unités</span>
                </div>
                <div className={`resource-change ${foodData?.netAfterPerishable >= 0 ? 'positive' : 'negative'}`}>
                  {foodData?.netAfterPerishable >= 0 ? '+' : ''}{formatNumber(foodData?.netAfterPerishable || 0)} unités/cycle
                </div>
                <div className="resource-details">
                  <div className="resource-detail-item">
                    <span>Production:</span>
                    <span className="positive">+{formatNumber(foodData?.production || 0)}</span>
                  </div>
                  <div className="resource-detail-item">
                    <span>Consommation:</span>
                    <span className="negative">-{formatNumber(foodData?.consumption || 0)}</span>
                  </div>
                  <div className="resource-detail-item">
                    <span>Péremption ({((foodData?.modifiers?.perishableRate || 0) * 100).toFixed(1)}%):</span>
                    <span className="negative">-{formatNumber(foodData?.perishableLoss || 0)}</span>
                  </div>
                </div>
              </div>

              {/* Population Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Population totale</div>
                  <div className="resource-icon">👥</div>
                </div>
                <div className="resource-value">
                  {formatNumber(jobs?.reduce((sum, job) => sum + (job?.number || 0), 0) || 0)}
                  <span className="resource-unit"> personnes</span>
                </div>
              </div>

              {/* Renown Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Renommée</div>
                  <div className="resource-icon">🏆</div>
                </div>
                <div className="resource-value">
                  {gameState?.renown || 0}
                </div>
              </div>

              {/* Mine Depth Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Profondeur de la mine</div>
                  <div className="resource-icon">⛰️</div>
                </div>
                <div className="resource-value">
                  {gameState?.mine_depth || 0}
                  <span className="resource-unit"> niveaux</span>
                </div>
              </div>

              {/* Sickness Probability Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Risque de maladie</div>
                  <div className="resource-icon">🤒</div>
                </div>
                <div className="resource-value">
                  {((gameState?.sick_probability || 0) * 100).toFixed(2)}
                  <span className="resource-unit">%</span>
                </div>
              </div>

              {/* Crime Probability Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Risque de crime</div>
                  <div className="resource-icon">🔪</div>
                </div>
                <div className="resource-value">
                  {((gameState?.crime_probability || 0) * 100).toFixed(1)}
                  <span className="resource-unit">%</span>
                </div>
              </div>

              {/* Research Probability Card */}
              <div className="resource-card">
                <div className="resource-header">
                  <div className="resource-title">Chance de découverte</div>
                  <div className="resource-icon">💡</div>
                </div>
                <div className="resource-value">
                  {((gameState?.research_probability || 0) * 100).toFixed(1)}
                  <span className="resource-unit">%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Food Tab */}
        <div className={`tab-content ${activeTab === 'food' ? 'active' : ''}`}>
          <FoodTabRedux />
        </div>

        {/* Mining Tab */}
        <div className={`tab-content ${activeTab === 'mining' ? 'active' : ''}`}>
          <MiningTabRedux gameState={{
            jobs: jobs,
            gameState: gameState,
            modifierTables: gameState?.modifierTables
          }} />
        </div>

        {/* Events Tab */}
        <div className={`tab-content ${activeTab === 'events' ? 'active' : ''}`}>
          <EventsTabRedux />
        </div>

        {/* Calendar Tab */}
        <div className={`tab-content ${activeTab === 'calendar' ? 'active' : ''}`}>
          <CalendarTabRedux />
        </div>

        {/* Population Tab */}
        <div className={`tab-content ${activeTab === 'population' ? 'active' : ''}`}>
          <PopulationTabRedux />
        </div>

        {/* Moral Tab */}
        <div className={`tab-content ${activeTab === 'moral' ? 'active' : ''}`}>
          <MoralTabRedux
            onMoralUpdated={() => dispatch(incrementMoralUpdated())}
          />
        </div>

        {/* Materials Tab */}
        <div className={`tab-content ${activeTab === 'materials' ? 'active' : ''}`}>
          <MaterialsTabRedux />
        </div>

        {/* Charges Tab */}
        <div className={`tab-content ${activeTab === 'charges' ? 'active' : ''}`}>
          <ChargesTabRedux gameState={{
            jobs: jobs,
            gameState: gameState,
            modifierTables: gameState?.modifierTables
          }} />
        </div>

        {/* Renown Tab */}
        <div className={`tab-content ${activeTab === 'renown' ? 'active' : ''}`}>
          <RenownTabRedux />
        </div>

        {/* Health Tab */}
        <div className={`tab-content ${activeTab === 'health' ? 'active' : ''}`}>
          <HealthTabRedux
            onHealthUpdated={() => dispatch(incrementHealthUpdated())}
          />
        </div>

        {/* Research Tab */}
        <div className={`tab-content ${activeTab === 'research' ? 'active' : ''}`}>
          <ResearchTabRedux
            onResearchUpdated={() => dispatch(incrementResearchUpdated())}
          />
        </div>

        {/* Trading Tab */}
        <div className={`tab-content ${activeTab === 'trading' ? 'active' : ''}`}>
          <TradingTabRedux
            onTradingUpdated={() => dispatch(incrementTradingUpdated())}
          />
        </div>

        {/* Justice and Defense Tab */}
        <div className={`tab-content ${activeTab === 'justice' ? 'active' : ''}`}>
          <JusticeDefenseTabRedux
            updateJusticeDefenseData={() => dispatch(incrementJusticeDefenseUpdated())}
          />
        </div>

        {/* Construction Tab */}
        <div className={`tab-content ${activeTab === 'construction' ? 'active' : ''}`}>
          <ConstructionTabRedux />
        </div>

        {/* Buildings Tab */}
        <div className={`tab-content ${activeTab === 'buildings' ? 'active' : ''}`}>
          <BuildingsTabRedux />
        </div>

        {/* Building Plans Tab */}
        <div className={`tab-content ${activeTab === 'building-plans' ? 'active' : ''}`}>
          <BuildingPlansTabRedux />
        </div>



        {/* L'onglet Redux Demo a été supprimé car toute l'application utilise maintenant Redux */}
      </div>
    </div>
  );
}

export default App;