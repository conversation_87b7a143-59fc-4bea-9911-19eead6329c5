import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import './ModifiersTable.css';
import { updateModifier, addModifier, deleteModifier } from '../services/ModifiersService';
import { fetchMoralModifiers } from '../store/slices/moralSlice';

const MoralModifiersTable = ({ title, tableId, modifiers, onModifierUpdated }) => {
  const dispatch = useDispatch();
  // État local pour stocker les modificateurs
  const [localModifiers, setLocalModifiers] = useState(modifiers || []);
  const [newModifier, setNewModifier] = useState({
    title: '',
    effect: 0,
    description: '',
    start_date: ''
  });
  const [editingModifier, setEditingModifier] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Synchroniser l'état local avec les props
  React.useEffect(() => {
    setLocalModifiers(modifiers || []);
  }, [modifiers]);

  // <PERSON>le adding a new modifier
  const handleAddModifier = async () => {
    if (!newModifier.title || newModifier.effect === '') {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    // Préparer les données du modificateur
    const modifierData = {
      title: newModifier.title,
      effect: parseInt(newModifier.effect), // Use integer for moral modifiers
      description: newModifier.description || '',
      start_date: newModifier.start_date || ''
    };

    try {
      console.log(`MoralModifiersTable: Ajout d'un modificateur à la table ${tableId}`, modifierData);

      // Utiliser directement le service pour garantir une mise à jour immédiate
      const result = await addModifier(tableId, modifierData);
      console.log(`MoralModifiersTable: Modificateur ajouté avec succès via Service`, result);

      // Reset the form
      setNewModifier({
        title: '',
        effect: 0,
        description: '',
        start_date: ''
      });

      // Hide the form
      setShowAddForm(false);

      // Forcer un rechargement des données Redux et attendre qu'il se termine
      await dispatch(fetchMoralModifiers());

      // Notify parent component to refresh AFTER Redux update is complete
      if (onModifierUpdated) {
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout du modificateur:', error);
      alert(`Échec de l'ajout du modificateur: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle editing a modifier
  const handleEditModifier = (modifier) => {
    setEditingModifier({
      ...modifier,
      effect: modifier.effect // No conversion needed for moral modifiers
    });
  };

  // Handle saving edited modifier
  const handleSaveEdit = async () => {
    if (!editingModifier || !editingModifier.title || editingModifier.effect === '') {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      console.log(`MoralModifiersTable: Mise à jour du modificateur ${editingModifier.id}`, editingModifier);

      const modifierData = {
        title: editingModifier.title,
        effect: parseInt(editingModifier.effect), // Use integer for moral modifiers
        description: editingModifier.description || '',
        start_date: editingModifier.start_date || ''
      };

      await updateModifier(editingModifier.id, modifierData);
      console.log(`MoralModifiersTable: Modificateur mis à jour avec succès`);

      // Reset editing state
      setEditingModifier(null);

      // Forcer un rechargement des données Redux et attendre qu'il se termine
      await dispatch(fetchMoralModifiers());

      // Notify parent component to refresh data AFTER Redux update is complete
      if (onModifierUpdated) {
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du modificateur:', error);
      alert(`Échec de la mise à jour du modificateur: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingModifier(null);
  };

  // Handle key press in edit form
  const handleEditKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  // Handle key press in new modifier form
  const handleNewModifierKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddModifier();
    }
  };

  // Handle deleting a modifier
  const handleDeleteModifier = async (modifierId) => {
    // Suppression du popup de confirmation

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      console.log(`MoralModifiersTable: Suppression du modificateur ${modifierId}`);

      // Utiliser directement le service pour garantir une mise à jour immédiate
      const result = await deleteModifier(modifierId);
      console.log(`MoralModifiersTable: Modificateur supprimé avec succès via Service`, result);

      // Forcer un rechargement des données Redux et attendre qu'il se termine
      await dispatch(fetchMoralModifiers());

      // Notify parent component to refresh AFTER Redux update is complete
      if (onModifierUpdated) {
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du modificateur:', error);
      alert(`Échec de la suppression du modificateur: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate total effect - vérifier que modifiers est un tableau et que les effets sont des nombres
  const displayModifiers = localModifiers || [];
  const totalEffect = Array.isArray(displayModifiers)
    ? displayModifiers.reduce((sum, mod) => {
        // Vérifier que mod et mod.effect sont définis
        if (!mod) return sum;
        const effect = typeof mod.effect === 'number' ? mod.effect : parseInt(mod.effect || '0') || 0;
        return sum + effect;
      }, 0)
    : 0;

  console.log(`MoralModifiersTable (${title}): Modifiers:`, displayModifiers);
  console.log(`MoralModifiersTable (${title}): Total Effect:`, totalEffect);

  // Get effect class for styling
  const getEffectClass = (effect) => {
    return effect >= 0 ? 'positive' : 'negative';
  };

  return (
    <div className="modifiers-table-container">
      <div className="modifiers-table-header">
        <h2>{title}</h2>
        <div className="total-effect">
          Total: <span className={getEffectClass(totalEffect)}>{totalEffect}</span>
        </div>
      </div>

      <table className="modifiers-table">
        <thead>
          <tr>
            <th>Intitulé</th>
            <th>Effet</th>
            <th>Début de l'effet</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {Array.isArray(displayModifiers) && displayModifiers.filter(mod => mod).map(modifier => (
            <tr key={modifier.id}>
              {editingModifier && editingModifier.id === modifier.id ? (
                // Edit mode
                <>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.title}
                      onChange={e => setEditingModifier({...editingModifier, title: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      autoFocus
                    />
                  </td>
                  <td>
                    <input
                      type="number"
                      value={editingModifier.effect}
                      onChange={e => setEditingModifier({...editingModifier, effect: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      style={{ width: '80px' }}
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.start_date || ''}
                      onChange={e => setEditingModifier({...editingModifier, start_date: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      placeholder="ex: 1349"
                      style={{ width: '100px' }}
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.description || ''}
                      onChange={e => setEditingModifier({...editingModifier, description: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      style={{ width: '100%' }}
                    />
                  </td>
                  <td>
                    <button
                      className="save-btn"
                      onClick={handleSaveEdit}
                    >
                      Sauvegarder
                    </button>
                    <button
                      className="cancel-btn"
                      onClick={handleCancelEdit}
                    >
                      Annuler
                    </button>
                  </td>
                </>
              ) : (
                // View mode
                <>
                  <td>{modifier.title}</td>
                  <td>
                    <span className={getEffectClass(modifier.effect)}>
                      {typeof modifier.effect === 'number' ? modifier.effect : parseInt(modifier.effect) || 0}
                    </span>
                  </td>
                  <td>{modifier.start_date || '-'}</td>
                  <td>{modifier.description}</td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="edit-btn"
                        onClick={() => handleEditModifier(modifier)}
                      >
                        Modifier
                      </button>
                      <button
                        className="delete-btn"
                        onClick={() => handleDeleteModifier(modifier.id)}
                      >
                        Supprimer
                      </button>
                    </div>
                  </td>
                </>
              )}
            </tr>
          ))}
        </tbody>
      </table>

      {!showAddForm ? (
        <div className="add-modifier-button-container">
          <button
            className="add-btn"
            onClick={() => setShowAddForm(true)}
          >
            + Ajouter un modificateur
          </button>
        </div>
      ) : (
        <div className="add-modifier-form">
          <h3>Ajouter un modificateur</h3>
          <div className="form-row">
            <div className="form-group">
              <label>Intitulé:</label>
              <input
                type="text"
                value={newModifier.title}
                onChange={e => setNewModifier({...newModifier, title: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
                autoFocus
              />
            </div>
            <div className="form-group">
              <label>Effet:</label>
              <input
                type="number"
                value={newModifier.effect}
                onChange={e => setNewModifier({...newModifier, effect: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
              />
            </div>
          </div>
          <div className="form-row">
            <div className="form-group">
              <label>Début de l'effet:</label>
              <input
                type="text"
                value={newModifier.start_date}
                onChange={e => setNewModifier({...newModifier, start_date: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
                placeholder="ex: 1349"
              />
            </div>
            <div className="form-group">
              <label>Description:</label>
              <input
                type="text"
                value={newModifier.description}
                onChange={e => setNewModifier({...newModifier, description: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
              />
            </div>
          </div>
          <div className="form-buttons">
            <button
              className="add-btn"
              onClick={handleAddModifier}
            >
              Ajouter
            </button>
            <button
              className="cancel-btn"
              onClick={() => {
                setShowAddForm(false);
                setNewModifier({
                  title: '',
                  effect: 0,
                  description: '',
                  start_date: ''
                });
              }}
            >
              Annuler
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MoralModifiersTable;
