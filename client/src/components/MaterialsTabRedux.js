import React from 'react';
import ModifiersTable from './ModifiersTable';
import './MaterialsTab.css';
import useMaterialsData from '../hooks/useMaterialsData';

/**
 * Version RTK Query du composant MaterialsTab
 * Cette version utilise RTK Query pour gérer les données en temps réel
 */
function MaterialsTabRedux() {
  // Use RTK Query hook for all materials data
  const {
    isLoading,
    error,
    materialsData,
    gameState,
    jobs,
    workers,
    activeWorkers,
    materialsGeneralModifiers,
    materialsTechModifiers,
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    MATERIALS_PER_WORKER,
    materialsGeneralTableId,
    materialsTechTableId
  } = useMaterialsData();

  // Function to handle modifier updates (used by ModifiersTable components)
  const handleModifierUpdated = () => {
    console.log('MaterialsTabRedux: handleModifierUpdated called - refetching data');
    refetchAll();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="materials-tab-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des données de matériaux...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="materials-tab-error">
        <h3>Erreur de chargement</h3>
        <p>{error?.message || 'Une erreur est survenue'}</p>
        <button onClick={refetchAll}>Réessayer</button>
      </div>
    );
  }

  // Extract materials data values
  const {
    currentReserves,
    production,
    baseProduction,
    generalEffects,
    techEffects,
    totalBonus
  } = materialsData;

  // Get miners information for display
  const miners = Array.isArray(jobs) ? jobs.find(j => j.name === 'Miner') : null;
  const activeMiners = (miners?.number || 0) - (miners?.sick || 0);

  return (
    <div className="materials-tab">
      <h2>Matériaux</h2>

      {/* Materials Stats Cards */}
      <div className="materials-stats-cards">
        <div className="materials-stat-card">
          <div className="materials-stat-icon">👷</div>
          <div className="materials-stat-title">Ouvriers actifs</div>
          <div className="materials-stat-value">{activeWorkers} ouvriers</div>
          <div className="materials-stat-description">Nombre d'ouvriers au travail</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">⛏️</div>
          <div className="materials-stat-title">Mineurs actifs</div>
          <div className="materials-stat-value">{activeMiners} mineurs</div>
          <div className="materials-stat-description">Nombre de mineurs au travail</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">🧱</div>
          <div className="materials-stat-title">Production de base</div>
          <div className="materials-stat-value">{formatNumber(baseProduction)} unités/cycle</div>
          <div className="materials-stat-description">{MATERIALS_PER_WORKER} unités par ouvrier, 2 unités par mineur</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">📈</div>
          <div className="materials-stat-title">Effet des modificateurs</div>
          <div className="materials-stat-value positive">{formatPercent(generalEffects + techEffects)}</div>
          <div className="materials-stat-description">Bonus total à la production</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">{getMoralEmoji(moralTitle)}</div>
          <div className="materials-stat-title">Moral: {moralTitle}</div>
          <div className={`materials-stat-value ${moralModifier >= 0 ? 'positive' : 'negative'}`}>
            {moralModifier >= 0 ? '+' : ''}{formatPercent(moralModifier).replace('%', '')}%
          </div>
          <div className="materials-stat-description">Impact du moral sur la production</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">🏭</div>
          <div className="materials-stat-title">Production totale</div>
          <div className="materials-stat-value">{formatNumber(production)} unités/cycle</div>
          <div className="materials-stat-description">Production de matériaux par cycle</div>
        </div>
      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Effets généraux sur les matériaux"
          tableId={materialsGeneralTableId}
          modifiers={materialsGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la génération de matériaux"
          tableId={materialsTechTableId}
          modifiers={materialsTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default MaterialsTabRedux;
