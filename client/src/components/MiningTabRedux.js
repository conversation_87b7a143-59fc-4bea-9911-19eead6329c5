import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import './MiningTab.css';
import {
  selectGameState,
  selectJobs,
  selectMiningData,
  selectModifierTables,
  fetchGameState
} from '../store/slices/gameSlice';
import { selectMoralStats } from '../store/slices/moralSlice';
import {
  fetchMiningModifiers,
  updateMiningStats,
  selectMiningGeneralModifiers,
  selectEngineeringModifiers,
  selectMiningTechModifiers,
  selectMiningStats
} from '../store/slices/miningSlice';

function MiningTabRedux({ gameState, updateMiningData, moralUpdated }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const reduxGameState = useSelector(selectGameState);
  const jobs = useSelector(selectJobs);
  const miningData = useSelector(selectMiningData);
  const modifierTables = useSelector(selectModifierTables);
  const moralStats = useSelector(selectMoralStats);

  // Sélectionner les données du slice miningSlice
  const miningGeneralModifiersFromSlice = useSelector(selectMiningGeneralModifiers);
  const engineeringModifiersFromSlice = useSelector(selectEngineeringModifiers);
  const miningTechModifiersFromSlice = useSelector(selectMiningTechModifiers);
  const miningStats = useSelector(selectMiningStats);

  // Table IDs
  const miningGeneralTableId = 4; // General mining effects
  const engineeringTableId = 5;   // Engineering effects
  const miningTechTableId = 6;    // Technology effects on mining

  // Get modifier tables for mining (pour les tables de modificateurs)
  const miningGeneralModifiers = modifierTables.filter(table => table.id === miningGeneralTableId);
  const engineeringModifiers = modifierTables.filter(table => table.id === engineeringTableId);
  const miningTechModifiers = modifierTables.filter(table => table.id === miningTechTableId);

  // Get miner and engineer information
  const miners = Array.isArray(jobs) ? jobs.find(j => j.name === 'Miner') : null;
  const engineers = Array.isArray(jobs) ? jobs.find(j => j.name === 'Engineer') : null;
  const activeMiners = (miners?.number || 0) - (miners?.sick || 0);
  const activeEngineers = (engineers?.number || 0) - (engineers?.sick || 0);

  // Charger les modificateurs au montage du composant et quand gameState change
  useEffect(() => {
    dispatch(fetchMiningModifiers());
  }, [dispatch, reduxGameState?.modifierTables, moralUpdated]);

  // Mettre à jour les statistiques de minage quand gameState ou moralStats change
  useEffect(() => {
    if (reduxGameState && moralStats) {
      console.log('MiningTabRedux: Mise à jour des statistiques avec moral:', moralStats);
      dispatch(updateMiningStats({
        gameState: reduxGameState,
        moralModifier: moralStats.modifier,
        moralTitle: moralStats.title
      }));
    }
  }, [dispatch, reduxGameState, moralStats]);

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = () => {
    console.log('MiningTabRedux: handleModifierUpdated appelé');
    // Recharger les modificateurs
    dispatch(fetchMiningModifiers()).then(() => {
      // Mettre à jour les statistiques avec le gameState actuel et les données de moral
      if (reduxGameState && moralStats) {
        console.log('MiningTabRedux: Mise à jour des statistiques après modification:', moralStats);
        dispatch(updateMiningStats({
          gameState: reduxGameState,
          moralModifier: moralStats.modifier,
          moralTitle: moralStats.title
        }));
      }
    });
    // Refetch game state to get updated calculations
    dispatch(fetchGameState({ force: true }));
  };

  // Use backend-calculated mining data or slice data
  const production = miningStats?.production || miningData?.production || 0;
  const moralTitle = moralStats?.title || 'Neutre';
  const moralModifier = moralStats?.modifier !== undefined ? moralStats.modifier : 0;

  // Calculate derived values - utiliser les données du slice si disponibles
  const revenuePerMiner = miningStats?.revenuePerMiner || (activeMiners > 0 ? production / activeMiners : 0);
  const engineeringMultiplier = activeEngineers * 0.03; // Base engineering multiplier

  // Get modifier values from tables
  const generalEffects = modifierTables.find(t => t.id === miningGeneralTableId)?.current_value || 0;
  const engineeringEffects = modifierTables.find(t => t.id === engineeringTableId)?.current_value || 0;
  const techEffects = modifierTables.find(t => t.id === miningTechTableId)?.current_value || 0;

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (value) => {
    if (value === undefined || value === null) return '0.0';
    return parseFloat(value).toFixed(1);
  };

  // Get emoji for moral title
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle.includes('Rébellion')) return '🤬';
    if (moralTitle.includes('En colère')) return '😠';
    if (moralTitle.includes('Triste')) return '😢';
    if (moralTitle.includes('Mécontent')) return '😒';
    if (moralTitle.includes('Neutre')) return '😐';
    if (moralTitle.includes('Content')) return '😊';
    if (moralTitle.includes('Epanoui')) return '😄';
    if (moralTitle.includes('Heureux')) return '😁';
    if (moralTitle.includes('Ecstatique')) return '🥳';

    return '😐'; // Default value
  };



  return (
    <div className="mining-tab">
      <h2>Minage</h2>

      {/* Mining Stats Cards */}
      <div className="mining-stats-cards">
        <div className="mining-stat-card">
          <div className="mining-stat-icon">⛏️</div>
          <div className="mining-stat-title">Mineurs actifs</div>
          <div className="mining-stat-value">{activeMiners} mineurs</div>
          <div className="mining-stat-description">Nombre de mineurs au travail</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">🧰</div>
          <div className="mining-stat-title">Revenus par mineur</div>
          <div className="mining-stat-value">{formatNumber(revenuePerMiner)} pièces/cycle</div>
          <div className="mining-stat-description">Incluant tous les bonus</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">🔧</div>
          <div className="mining-stat-title">Bonus d'ingénierie</div>
          <div className="mining-stat-value positive">{formatPercent(engineeringMultiplier)}</div>
          <div className="mining-stat-description">Bonus des ingénieurs</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">📈</div>
          <div className="mining-stat-title">Effet des modificateurs</div>
          <div className="mining-stat-value positive">{formatPercent(generalEffects + techEffects)}</div>
          <div className="mining-stat-description">Bonus total à la production</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">{getMoralEmoji(moralTitle)}</div>
          <div className="mining-stat-title">Moral: {moralTitle}</div>
          <div className={`mining-stat-value ${moralModifier >= 0 ? 'positive' : 'negative'}`}>
            {moralModifier >= 0 ? '+' : ''}{formatPercent(moralModifier).replace('%', '')}%
          </div>
          <div className="mining-stat-description">Impact du moral sur la production</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">💰</div>
          <div className="mining-stat-title">Production totale</div>
          <div className="mining-stat-value">{formatNumber(production)} pièces/cycle</div>
          <div className="mining-stat-description">Revenus de la mine par cycle</div>
        </div>
      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Effets généraux sur la mine"
          tableId={miningGeneralTableId}
          modifiers={miningGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Ingénierie"
          tableId={engineeringTableId}
          modifiers={engineeringModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la mine"
          tableId={miningTechTableId}
          modifiers={miningTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default MiningTabRedux;
