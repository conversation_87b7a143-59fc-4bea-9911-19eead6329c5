import React from 'react';
import ModifiersTable from './ModifiersTable';
import './MiningTab.css';
import useMiningData from '../hooks/useMiningData';

function MiningTabRedux() {
  // Use RTK Query hook for all mining data
  const {
    isLoading,
    error,
    miningData,
    gameState,
    jobs,
    miners,
    engineers,
    activeMiners,
    activeEngineers,
    miningGeneralModifiers,
    engineeringModifiers,
    miningTechModifiers,
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    MINING_VALUE,
    miningGeneralTableId,
    engineeringTableId,
    miningTechTableId
  } = useMiningData();

  // Function to handle modifier updates (used by ModifiersTable components)
  const handleModifierUpdated = () => {
    console.log('MiningTabRedux: handleModifierUpdated called - refetching data');
    refetchAll();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="mining-tab-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des données de minage...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="mining-tab-error">
        <h3>Erreur de chargement</h3>
        <p>{error?.message || 'Une erreur est survenue'}</p>
        <button onClick={refetchAll}>Réessayer</button>
      </div>
    );
  }

  // Extract mining data values (all functions and data come from the hook)
  const {
    production,
    baseProduction,
    revenuePerMiner,
    totalBonus,
    engineeringMultiplier,
    generalEffects,
    techEffects,
    engineeringEffects
  } = miningData;



  return (
    <div className="mining-tab">
      <h2>Minage</h2>

      {/* Mining Stats Cards */}
      <div className="mining-stats-cards">
        <div className="mining-stat-card">
          <div className="mining-stat-icon">⛏️</div>
          <div className="mining-stat-title">Mineurs actifs</div>
          <div className="mining-stat-value">{activeMiners} mineurs</div>
          <div className="mining-stat-description">Nombre de mineurs au travail</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">🧰</div>
          <div className="mining-stat-title">Revenus par mineur</div>
          <div className="mining-stat-value">{formatNumber(revenuePerMiner)} pièces/cycle</div>
          <div className="mining-stat-description">Incluant tous les bonus</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">🔧</div>
          <div className="mining-stat-title">Bonus d'ingénierie</div>
          <div className="mining-stat-value positive">{formatPercent(engineeringMultiplier)}</div>
          <div className="mining-stat-description">Bonus des ingénieurs</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">📈</div>
          <div className="mining-stat-title">Effet des modificateurs</div>
          <div className="mining-stat-value positive">{formatPercent(generalEffects + techEffects)}</div>
          <div className="mining-stat-description">Bonus total à la production</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">{getMoralEmoji(moralTitle)}</div>
          <div className="mining-stat-title">Moral: {moralTitle}</div>
          <div className={`mining-stat-value ${moralModifier >= 0 ? 'positive' : 'negative'}`}>
            {moralModifier >= 0 ? '+' : ''}{formatPercent(moralModifier).replace('%', '')}%
          </div>
          <div className="mining-stat-description">Impact du moral sur la production</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">💰</div>
          <div className="mining-stat-title">Production totale</div>
          <div className="mining-stat-value">{formatNumber(production)} pièces/cycle</div>
          <div className="mining-stat-description">Revenus de la mine par cycle</div>
        </div>
      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Effets généraux sur la mine"
          tableId={miningGeneralTableId}
          modifiers={miningGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Ingénierie"
          tableId={engineeringTableId}
          modifiers={engineeringModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la mine"
          tableId={miningTechTableId}
          modifiers={miningTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default MiningTabRedux;
