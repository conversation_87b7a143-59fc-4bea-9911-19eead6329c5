import React from 'react';
import { Al<PERSON>, Spin } from 'antd';
import MoralModifiersTable from './MoralModifiersTable';
import './MoralTab.css';
import useMoralData from '../hooks/useMoralData';

/**
 * Version RTK Query du composant MoralTab
 * Cette version utilise RTK Query pour gérer les données en temps réel
 */
function MoralTabRedux() {
  // Use RTK Query hook for all moral data
  const {
    isLoading,
    error,
    moralData,
    gameState,
    jobs,
    moralModifiers,
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    moralTableId
  } = useMoralData();

  // Function to handle modifier updates (used by ModifiersTable components)
  const handleModifierUpdated = () => {
    console.log('MoralTabRedux: handleModifierUpdated called - refetching data');
    refetchAll();
  };



  // Show loading state
  if (isLoading) {
    return (
      <div className="moral-tab loading">
        <Spin size="large" />
        <p>Chargement des données de moral...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="moral-tab">
        <Alert
          message="Erreur"
          description={error?.message || 'Une erreur est survenue'}
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div className="moral-tab">
      <h2>Moral de la colonie</h2>

      <div className="moral-main-container">
        {/* Panneau de gauche: Valeur globale du moral avec emoji */}
        <div className="moral-left-panel">
          <div className="moral-global-circle">
            <div className="moral-circle" style={{ backgroundColor: moralData.currentValue >= 1000 ? '#52c41a' : '#f5222d' }}>
              <div className="moral-emoji">
                {getMoralEmoji(moralData.title)}
              </div>
              <div className="moral-value">
                {moralData.currentValue || 1000}
              </div>
            </div>
            <div className="moral-title">
              {moralData.title || 'Neutre'}
              {/* Afficher l'emoji à côté du titre pour déboguer */}
              <span style={{ marginLeft: '10px' }}>{getMoralEmoji(moralData.title)}</span>
            </div>
          </div>
        </div>

        {/* Panneau de droite: Effets permanents */}
        <div className="moral-right-panel">
          <div className="moral-effects-card">
            <h3 className="effects-title">Effets permanents</h3>

            <div className="effect-row">
              <div className="effect-label">Logement:</div>
              <div
                className="effect-value"
                style={{ color: moralData.housingEffect >= 0 ? '#52c41a' : '#f5222d' }}
              >
                {moralData.housingEffect < 0 ? moralData.housingEffect : `+${moralData.housingEffect}`} points
              </div>
              <div className="effect-info">
                {moralData.dwellingsAvailable < 0
                  ? `${Math.abs(moralData.dwellingsAvailable)} personnes sans logement`
                  : `${moralData.dwellingsAvailable} logements disponibles`}
              </div>
            </div>

            <div className="effect-row">
              <div className="effect-label">Population:</div>
              <div
                className="effect-value"
                style={{ color: moralData.populationEffect >= 0 ? '#52c41a' : '#f5222d' }}
              >
                {moralData.populationEffect < 0 ? moralData.populationEffect : `+${moralData.populationEffect}`} points
              </div>
              <div className="effect-info">
                {moralData.totalInhabitants} habitants
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Impact sur la productivité */}
      <div className="productivity-card">
        <h3 className="effects-title">Impact sur la productivité</h3>
        <div className="productivity-row">
          <div className="productivity-label">Productivité:</div>
          <div
            className="productivity-value"
            style={{ color: moralData.productivityPercentage >= 0 ? '#52c41a' : '#f5222d' }}
          >
            {moralData.productivityPercentage >= 0
              ? `+${moralData.productivityPercentage}%`
              : `${moralData.productivityPercentage}%`}
          </div>
        </div>
      </div>

      {/* Table des modificateurs */}
      <div className="moral-modifiers">
        <MoralModifiersTable
          title="Modificateurs de moral"
          tableId={moralTableId}
          modifiers={moralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default MoralTabRedux;
