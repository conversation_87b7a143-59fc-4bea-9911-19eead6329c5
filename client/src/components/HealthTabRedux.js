import React from 'react';
import ModifiersTable from './ModifiersTable';
import './HealthTab.css';
import useHealthData from '../hooks/useHealthData';

/**
 * Version RTK Query du composant HealthTab
 * Cette version utilise RTK Query pour gérer les données en temps réel
 */
function HealthTabRedux({ onHealthUpdated }) {
  // Use RTK Query hook for all health data
  const {
    isLoading,
    error,
    healthData,
    gameState,
    jobs,
    doctors,
    activeDoctors,
    healthGeneralModifiers,
    healthTechModifiers,
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    healthGeneralTableId,
    healthTechTableId
  } = useHealthData();

  // Function to handle modifier updates (used by ModifiersTable components)
  const handleModifierUpdated = () => {
    console.log('HealthTabRedux: handleModifierUpdated called - refetching data');
    refetchAll();

    // Notifier le parent si nécessaire
    if (onHealthUpdated) {
      onHealthUpdated();
    }
  };

  // Format small probability as percentage with more precision
  const formatProbability = (value) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="health-tab-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des données de santé...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="health-tab-error">
        <h3>Erreur de chargement</h3>
        <p>{error?.message || 'Une erreur est survenue'}</p>
        <button onClick={refetchAll}>Réessayer</button>
      </div>
    );
  }

  // Extract health data values
  const {
    sicknessProbability,
    baseSicknessProbability,
    doctorEffect,
    generalEffects,
    techEffects,
    totalSick,
    sickByJob
  } = healthData;

  return (
    <div className="health-tab">
      <h2>Santé</h2>

      {/* Health Stats Cards */}
      <div className="health-stats-cards">
        <div className="health-stat-card">
          <div className="health-stat-icon">🤒</div>
          <div className="health-stat-title">Malades</div>
          <div className="health-stat-value">{totalSick}</div>
          <div className="health-stat-description">Nombre total de malades</div>
        </div>

        <div className="health-stat-card">
          <div className="health-stat-icon">📊</div>
          <div className="health-stat-title">Taux de maladie</div>
          <div className="health-stat-value">{formatPercent(totalSick / (jobs?.reduce((sum, job) => sum + (job?.number || 0), 0) || 1) * 100)}</div>
          <div className="health-stat-description">Pourcentage de la population malade</div>
        </div>

        <div className="health-stat-card">
          <div className="health-stat-icon">⚕️</div>
          <div className="health-stat-title">Docteurs</div>
          <div className="health-stat-value">{activeDoctors}</div>
          <div className="health-stat-description">Nombre de docteurs actifs</div>
        </div>

        <div className="health-stat-card">
          <div className="health-stat-icon">🔮</div>
          <div className="health-stat-title">Risque de maladie</div>
          <div className="health-stat-value">{formatProbability(sicknessProbability)}</div>
          <div className="health-stat-description">Probabilité de maladie au prochain cycle</div>
        </div>
      </div>

      {/* Health Information Card */}
      <div className="health-info-card">
        <h3>Système de santé</h3>
        <p>Les malades sont calculés au début de chaque mois en fonction du risque de maladie. Tous les malades sont automatiquement guéris après que les productions aient été générées.</p>
        <p>Les docteurs réduisent le risque de maladie dans la colonie. Plus vous avez de docteurs, plus le risque de maladie diminue.</p>
      </div>

      {/* Health Modifiers Tables */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Modificateurs généraux de santé"
          tableId={healthGeneralTableId}
          modifiers={healthGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la santé"
          tableId={healthTechTableId}
          modifiers={healthTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default HealthTabRedux;
