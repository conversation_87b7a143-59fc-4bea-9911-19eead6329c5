import React from 'react';
import ModifiersTable from './ModifiersTable';
import './RenownTab.css';
import useRenownData from '../hooks/useRenownData';

/**
 * Version RTK Query du composant RenownTab
 * Cette version utilise RTK Query pour gérer les données en temps réel
 */
function RenownTabRedux() {
  // Use RTK Query hook for all renown data
  const {
    isLoading,
    error,
    renownData,
    gameState,
    jobs,
    renownGeneralModifiers,
    renownTechModifiers,
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    renownGeneralTableId,
    renownTechTableId
  } = useRenownData();

  // Function to handle modifier updates (used by ModifiersTable components)
  const handleModifierUpdated = () => {
    console.log('RenownTabRedux: handleModifierUpdated called - refetching data');
    refetchAll();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="renown-tab-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des données de renommée...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="renown-tab-error">
        <h3>Erreur de chargement</h3>
        <p>{error?.message || 'Une erreur est survenue'}</p>
        <button onClick={refetchAll}>Réessayer</button>
      </div>
    );
  }

  // Extract renown data values
  const {
    currentRenown
  } = renownData;

  return (
    <div className="renown-tab">
      <div className="renown-stats">
        <div className="renown-stats-card">
          <div className="renown-stat-icon">🏆</div>
          <div className="renown-stat-title">Renommée actuelle</div>
          <div className="renown-stat-value">{Math.round(currentRenown)}</div>
          <div className="renown-stat-description">
            Points de renommée accumulés
          </div>
        </div>


      </div>

      <div className="modifiers-tables">
        <ModifiersTable
          title="Modificateurs généraux de renommée"
          tableId={renownGeneralTableId}
          modifiers={renownGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
          useAbsoluteValues={true} // Utiliser des valeurs entières, pas des pourcentages
        />

        <ModifiersTable
          title="Technologies qui affectent la renommée"
          tableId={renownTechTableId}
          modifiers={renownTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default RenownTabRedux;
