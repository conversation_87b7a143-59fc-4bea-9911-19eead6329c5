import React, { useState } from 'react';
import { useGetEventsQuery } from '../store/api/gameApi';
import './EventsTab.css';

const EventsTabRedux = () => {
  const [expandedEvents, setExpandedEvents] = useState(new Set());

  const {
    data: events,
    error,
    isLoading,
    refetch
  } = useGetEventsQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const toggleEventExpansion = (eventId) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId);
    } else {
      newExpanded.add(eventId);
    }
    setExpandedEvents(newExpanded);
  };

  const getEventIcon = (eventType) => {
    switch (eventType) {
      case 'SICKNESS': return '🤒';
      case 'CRIME': return '🔪';
      case 'RESEARCH': return '💡';
      case 'FAMINE': return '🍽️';
      case 'TREASURY': return '💰';
      case 'BUILDING': return '🏗️';
      case 'POPULATION': return '👥';
      case 'MORAL': return '😊';
      default: return '📝';
    }
  };

  if (isLoading) {
    return <div className="loading">Chargement des événements...</div>;
  }

  if (error) {
    return (
      <div className="error">
        <p>Erreur lors du chargement des événements: {error.message || 'Erreur inconnue'}</p>
        <button onClick={() => refetch()}>Réessayer</button>
      </div>
    );
  }

  return (
    <div className="events-tab">
      <div className="events-header">
        <h2>Journal des Événements</h2>
        <button className="refresh-btn" onClick={() => refetch()}>
          🔄 Actualiser
        </button>
      </div>

      {events && events.length > 0 ? (
        <div className="events-list-accordion">
          {events.map((event, index) => {
            const isExpanded = expandedEvents.has(event.id || index);
            return (
              <div key={event.id || index} className={`event-accordion-item ${event.event_type?.toLowerCase() || 'default'}`}>
                <div
                  className="event-accordion-header"
                  onClick={() => toggleEventExpansion(event.id || index)}
                >
                  <div className="event-summary">
                    <div className="event-icon">
                      {getEventIcon(event.event_type)}
                    </div>
                    <div className="event-basic-info">
                      <div className="event-cycle">Cycle {event.cycle}</div>
                      <div className="event-preview">
                        {event.description?.substring(0, 80)}
                        {event.description?.length > 80 ? '...' : ''}
                      </div>
                    </div>
                  </div>
                  <div className="event-expand-icon">
                    {isExpanded ? '▼' : '▶'}
                  </div>
                </div>

                {isExpanded && (
                  <div className="event-accordion-content">
                    <div className="event-full-description">
                      {event.description}
                    </div>
                    <div className="event-metadata">
                      <div className="event-type">Type: {event.event_type}</div>
                      {event.created_at && (
                        <div className="event-date">
                          Date: {new Date(event.created_at).toLocaleString('fr-FR')}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      ) : (
        <div className="no-events">
          <p>Aucun événement n'a été enregistré.</p>
          <p>Les événements apparaîtront ici au fur et à mesure que vous progressez dans le jeu.</p>
        </div>
      )}
    </div>
  );
};

export default EventsTabRedux;
