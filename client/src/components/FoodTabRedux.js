import React from 'react';
import ModifiersTable from './ModifiersTable';
import './FoodTab.css';
import useFoodData from '../hooks/useFoodData';

function FoodTabRedux() {
  // Use RTK Query hook for all food data
  const {
    isLoading,
    error,
    foodData,
    gameState,
    jobs,
    farmers,
    effectiveFarmers,
    foodGeneralModifiers,
    foodTechModifiers,
    perishableModifiers,
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    FOOD_PER_FARMER,
    foodGeneralTableId,
    foodTechTableId,
    perishableTableId
  } = useFoodData();

  // Function to handle modifier updates (used by ModifiersTable components)
  const handleModifierUpdated = () => {
    console.log('FoodTabRedux: handleModifierUpdated called - refetching data');
    refetchAll();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="food-tab-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des données alimentaires...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="food-tab-error">
        <h3>Erreur de chargement</h3>
        <p>{error?.message || 'Une erreur est survenue'}</p>
        <button onClick={refetchAll}>Réessayer</button>
      </div>
    );
  }

  // Extract food data values (all functions and data come from the hook)
  const {
    currentReserves,
    production,
    consumption,
    net,
    perishableLoss,
    netAfterPerishable,
    seasonFactor,
    perishableRate,
    generalEffects,
    techEffects
  } = foodData;

  return (
    <div className="food-tab">
      <h2>Production de Nourriture</h2>

      {/* Food Stats Cards */}
      <div className="food-stats-cards">
        <div className="food-stat-card">
          <div className="food-stat-icon">🍎</div>
          <div className="food-stat-title">Réserves actuelles</div>
          <div className="food-stat-value">{formatNumber(currentReserves)} unités</div>
          <div className="food-stat-description">Quantité de nourriture disponible</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🗑️</div>
          <div className="food-stat-title">Péremption</div>
          <div className="food-stat-value negative">-{formatNumber(perishableLoss)} unités/cycle</div>
          <div className="food-stat-description">{formatPercent(perishableRate)} des réserves actuelles</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🌾</div>
          <div className="food-stat-title">Production</div>
          <div className="food-stat-value positive">+{formatNumber(production)} unités/cycle</div>
          <div className="food-stat-description">Nourriture produite chaque cycle</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🍽️</div>
          <div className="food-stat-title">Consommation</div>
          <div className="food-stat-value negative">-{formatNumber(consumption)} unités/cycle</div>
          <div className="food-stat-description">Nourriture consommée chaque cycle</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">⚖️</div>
          <div className="food-stat-title">Bilan par cycle</div>
          <div className={`food-stat-value ${netAfterPerishable >= 0 ? 'positive' : 'negative'}`}>
            {netAfterPerishable >= 0 ? '+' : ''}{formatNumber(netAfterPerishable)} unités
          </div>
          <div className="food-stat-description">Production moins consommation et péremption</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🌡️</div>
          <div className="food-stat-title">Facteur saisonnier</div>
          <div className="food-stat-value">{formatPercent(seasonFactor)}</div>
          <div className="food-stat-description">Effet de la saison sur la production</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">👨‍🌾</div>
          <div className="food-stat-title">Nourriture par fermier</div>
          <div className="food-stat-value">{FOOD_PER_FARMER} unités</div>
          <div className="food-stat-description">Production de base par fermier</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">📈</div>
          <div className="food-stat-title">Effet des modificateurs</div>
          <div className="food-stat-value positive">+{formatPercent(generalEffects + techEffects)}</div>
          <div className="food-stat-description">Bonus total à la production ({formatPercent(generalEffects)} général, {formatPercent(techEffects)} tech)</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">{getMoralEmoji(moralTitle)}</div>
          <div className="food-stat-title">Moral: {moralTitle}</div>
          <div className={`food-stat-value ${moralModifier >= 0 ? 'positive' : 'negative'}`}>
            {moralModifier >= 0 ? '+' : ''}{formatPercent(moralModifier)}
          </div>
          <div className="food-stat-description">Impact du moral sur la production</div>
        </div>


      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Effets généraux sur la nourriture"
          tableId={foodGeneralTableId}
          modifiers={foodGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la production de nourriture"
          tableId={foodTechTableId}
          modifiers={foodTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Péremption des vivres"
          tableId={perishableTableId}
          modifiers={perishableModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default FoodTabRedux;
