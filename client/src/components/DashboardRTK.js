import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  useGetDashboardQuery,
  useGetGameStateQuery
} from '../store/api/gameApi';
import { wsConnect } from '../middleware/websocketRTKMiddleware';

/**
 * Custom hook for dashboard data using RTK Query for real-time data
 */
const useDashboardData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    error: dashboardError,
    isLoading: dashboardLoading,
    refetch: refetchDashboard
  } = useGetDashboardQuery(undefined, {
    pollingInterval: 30000, // Poll every 30 seconds as fallback
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Connect to WebSocket on hook mount
  useEffect(() => {
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    dispatch(wsConnect(wsUrl));
  }, [dispatch]);

  // Format numbers for display
  const formatNumber = (num) => {
    if (num === null || num === undefined) return '0';
    return Math.round(num).toLocaleString();
  };

  // formatPercent function removed as it's not currently used

  // Calculate financial data
  const calculateFinancialData = () => {
    if (!gameState) return { currentTreasure: 0, netFinances: 0, miningRevenue: 0, tradingRevenue: 0, totalExpenses: 0 };

    const currentTreasure = gameState.treasure || 0;
    const miningRevenue = gameState.mining_revenue || 0;
    const tradingRevenue = gameState.trading_revenue || 0;
    const totalExpenses = (gameState.salaries || 0) + (gameState.non_salary_charges || 0);
    const netFinances = miningRevenue + tradingRevenue - totalExpenses;

    return {
      currentTreasure,
      netFinances,
      miningRevenue,
      tradingRevenue,
      totalExpenses
    };
  };

  const { currentTreasure, netFinances, miningRevenue, tradingRevenue, totalExpenses } = calculateFinancialData();

  // Show loading state
  if (dashboardLoading || gameStateLoading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Chargement du tableau de bord...</p>
      </div>
    );
  }

  // Show error state
  if (dashboardError || gameStateError) {
    return (
      <div className="dashboard-error">
        <h3>Erreur de chargement</h3>
        <p>{dashboardError?.message || gameStateError?.message || 'Une erreur est survenue'}</p>
        <button onClick={refetchDashboard}>Réessayer</button>
      </div>
    );
  }

  // Return the data for the original dashboard to use
  return {
    gameState,
    currentTreasure,
    netFinances,
    miningRevenue,
    tradingRevenue,
    totalExpenses,
    formatNumber,
    isLoading: dashboardLoading || gameStateLoading,
    error: dashboardError || gameStateError,
    refetch: refetchDashboard
  };
};

export default useDashboardData;
