import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  useGetDashboardQuery,
  useGetGameStateQuery,
  useProcessNextCycleMutation
} from '../store/api/gameApi';
import { wsConnect } from '../middleware/websocketRTKMiddleware';

/**
 * Enhanced Dashboard component using RTK Query for real-time data
 */
const DashboardRTK = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    error: dashboardError,
    isLoading: dashboardLoading,
    refetch: refetchDashboard
  } = useGetDashboardQuery(undefined, {
    pollingInterval: 30000, // Poll every 30 seconds as fallback
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Mutation for processing next cycle
  const [processNextCycle, { isLoading: cycleLoading }] = useProcessNextCycleMutation();

  // Connect to WebSocket on component mount
  useEffect(() => {
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    dispatch(wsConnect(wsUrl));
  }, [dispatch]);

  // Handle next cycle
  const handleNextCycle = async () => {
    try {
      await processNextCycle().unwrap();
      // RTK Query will automatically refetch data due to cache invalidation
    } catch (error) {
      console.error('Error processing cycle:', error);
    }
  };

  // Format numbers for display
  const formatNumber = (num) => {
    if (num === null || num === undefined) return '0';
    return Math.round(num).toLocaleString();
  };

  // formatPercent function removed as it's not currently used

  // Calculate financial data
  const calculateFinancialData = () => {
    if (!gameState) return { currentTreasure: 0, netFinances: 0, miningRevenue: 0, tradingRevenue: 0, totalExpenses: 0 };

    const currentTreasure = gameState.treasure || 0;
    const miningRevenue = gameState.mining_revenue || 0;
    const tradingRevenue = gameState.trading_revenue || 0;
    const totalExpenses = (gameState.salaries || 0) + (gameState.non_salary_charges || 0);
    const netFinances = miningRevenue + tradingRevenue - totalExpenses;

    return {
      currentTreasure,
      netFinances,
      miningRevenue,
      tradingRevenue,
      totalExpenses
    };
  };

  const { currentTreasure, netFinances, miningRevenue, tradingRevenue, totalExpenses } = calculateFinancialData();

  // Show loading state
  if (dashboardLoading || gameStateLoading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Chargement du tableau de bord...</p>
      </div>
    );
  }

  // Show error state
  if (dashboardError || gameStateError) {
    return (
      <div className="dashboard-error">
        <h3>Erreur de chargement</h3>
        <p>{dashboardError?.message || gameStateError?.message || 'Une erreur est survenue'}</p>
        <button onClick={refetchDashboard}>Réessayer</button>
      </div>
    );
  }

  return (
    <div className="dashboard-rtk">
      {/* Header */}
      <header className="App-header">
        <div className="header-container">
          <h1>Gestion de la Mine</h1>
          <div>
            <button
              className="next-cycle-btn"
              onClick={handleNextCycle}
              disabled={cycleLoading}
            >
              {cycleLoading ? 'Traitement...' : 'Exécuter un cycle'}
            </button>
          </div>
        </div>

        {!gameState && (
          <div style={{
            margin: '10px 0',
            padding: '10px',
            backgroundColor: '#ffcdd2',
            borderRadius: '4px',
            textAlign: 'center',
            fontWeight: 'bold'
          }}>
            ATTENTION: Aucune donnée n'est disponible. Le dashboard peut afficher des informations incorrectes ou incomplètes.
          </div>
        )}
      </header>

      {/* Game Info Section */}
      <div className="game-info">
        <div className="game-info-item">
          <div className="game-info-label">Cycle:</div>
          <div className="game-info-value">{gameState?.cycle_number || 'N/A'}</div>
        </div>
        <div className="game-info-item">
          <div className="game-info-label">Mois:</div>
          <div className="game-info-value">{gameState?.month || 'N/A'}</div>
        </div>
        <div className="game-info-item">
          <div className="game-info-label">Année:</div>
          <div className="game-info-value">{gameState?.year || 'N/A'}</div>
        </div>
        <div className="game-info-item">
          <div className="game-info-label">Saison:</div>
          <div className="game-info-value">
            {gameState?.season || 'N/A'}
            {gameState?.season_description && (
              <div className="season-description">
                {gameState.season_description}
              </div>
            )}
          </div>
        </div>
        <div className="game-info-item">
          <div className="game-info-label">Population:</div>
          <div className="game-info-value">{gameState?.population || 'N/A'}</div>
        </div>
        <div className="game-info-item">
          <div className="game-info-label">Niveau tech:</div>
          <div className="game-info-value">{gameState?.tech_level || 'N/A'}</div>
        </div>
      </div>

      {/* Resources Grid */}
      <div className="section">
        <h2 className="section-title">Ressources</h2>
        <div className="resources-grid">
          {/* Treasury Card */}
          <div className="resource-card">
            <div className="resource-header">
              <div className="resource-title">Finances</div>
              <div className="resource-icon">💰</div>
            </div>
            <div className="resource-value">
              {formatNumber(currentTreasure)}
              <span className="resource-unit"> or</span>
            </div>
            <div className={`resource-change ${netFinances >= 0 ? 'positive' : 'negative'}`}>
              {netFinances >= 0 ? '+' : ''}{formatNumber(netFinances)} or/cycle
            </div>
            <div className="resource-details">
              <div className="resource-detail-item">
                <span>Revenus miniers:</span>
                <span className="positive">+{formatNumber(miningRevenue)}</span>
              </div>
              <div className="resource-detail-item">
                <span>Revenus commerciaux:</span>
                <span className="positive">+{formatNumber(tradingRevenue)}</span>
              </div>
              <div className="resource-detail-item">
                <span>Dépenses totales:</span>
                <span className="negative">-{formatNumber(totalExpenses)}</span>
              </div>
            </div>
          </div>

          {/* Food Card */}
          <div className="resource-card">
            <div className="resource-header">
              <div className="resource-title">Nourriture</div>
              <div className="resource-icon">🍎</div>
            </div>
            <div className="resource-value">
              {formatNumber(gameState?.food_reserves || 0)}
              <span className="resource-unit"> unités</span>
            </div>
            <div className={`resource-change ${(gameState?.food_net || 0) >= 0 ? 'positive' : 'negative'}`}>
              {(gameState?.food_net || 0) >= 0 ? '+' : ''}{formatNumber(gameState?.food_net || 0)} unités/cycle
            </div>
            <div className="resource-details">
              <div className="resource-detail-item">
                <span>Production:</span>
                <span className="positive">+{formatNumber(gameState?.food_production || 0)}</span>
              </div>
              <div className="resource-detail-item">
                <span>Consommation:</span>
                <span className="negative">-{formatNumber(gameState?.food_consumption || 0)}</span>
              </div>
            </div>
          </div>

          {/* Materials Card */}
          <div className="resource-card">
            <div className="resource-header">
              <div className="resource-title">Matériaux</div>
              <div className="resource-icon">🔨</div>
            </div>
            <div className="resource-value">
              {formatNumber(gameState?.materials_reserves || 0)}
              <span className="resource-unit"> unités</span>
            </div>
            <div className={`resource-change ${(gameState?.materials_net || 0) >= 0 ? 'positive' : 'negative'}`}>
              {(gameState?.materials_net || 0) >= 0 ? '+' : ''}{formatNumber(gameState?.materials_net || 0)} unités/cycle
            </div>
          </div>

          {/* Moral Card */}
          <div className="resource-card">
            <div className="resource-header">
              <div className="resource-title">Moral</div>
              <div className="resource-icon">😊</div>
            </div>
            <div className="resource-value">
              {formatNumber(gameState?.moral || 0)}
              <span className="resource-unit"></span>
            </div>
            <div className="resource-description">
              Moral de la population
            </div>
          </div>

          {/* Research Probability Card */}
          <div className="resource-card">
            <div className="resource-header">
              <div className="resource-title">Chance de découverte</div>
              <div className="resource-icon">💡</div>
            </div>
            <div className="resource-value">
              {((gameState?.research_probability || 0) * 100).toFixed(1)}
              <span className="resource-unit">%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardRTK;
