import React from 'react';
import ModifiersTable from './ModifiersTable';
import ModifiersTableAbsolute from './ModifiersTableAbsolute';
import './ChargesTab.css';
import useChargesData from '../hooks/useChargesData';

/**
 * Version RTK Query du composant ChargesTab
 * Cette version utilise RTK Query pour gérer les données en temps réel
 */
function ChargesTabRedux() {
  // Use RTK Query hook for all charges data
  const {
    isLoading,
    error,
    chargesData,
    gameState,
    jobs,
    chargesGeneralModifiers,
    chargesTechModifiers,
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    chargesGeneralTableId,
    chargesTechTableId
  } = useChargesData();

  // Function to handle modifier updates (used by ModifiersTable components)
  const handleModifierUpdated = () => {
    console.log('ChargesTabRedux: handleModifierUpdated called - refetching data');
    refetchAll();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="charges-tab-loading">
        <div className="loading-spinner"></div>
        <p>Chargement des données de charges...</p>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="charges-tab-error">
        <h3>Erreur de chargement</h3>
        <p>{error?.message || 'Une erreur est survenue'}</p>
        <button onClick={refetchAll}>Réessayer</button>
      </div>
    );
  }

  // Extract charges data values
  const {
    totalSalaries,
    nonSalaryCharges,
    totalCharges,
    salaryBreakdown,
    generalEffects,
    techEffects,
    craftsmanEffect
  } = chargesData;

  return (
    <div className="charges-tab">
      <h2>Charges</h2>

      {/* Charges Stats Cards */}
      <div className="charges-stats-cards">
        <div className="charges-stat-card">
          <div className="charges-stat-icon">💰</div>
          <div className="charges-stat-title">Charges fixes</div>
          <div className="charges-stat-value">
            {formatNumber(generalEffects, 2)} or/cycle
          </div>
          <div className="charges-stat-description">Somme des coûts généraux</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">👨‍💼</div>
          <div className="charges-stat-title">Effet des artisans</div>
          <div className="charges-stat-value positive">
            {formatPercent(craftsmanEffect ? (1/(1+craftsmanEffect))-1 : 0)}
          </div>
          <div className="charges-stat-description">Réduction des charges non salariales (Max: -50%)</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">📊</div>
          <div className="charges-stat-title">Modificateurs généraux</div>
          <div className={`charges-stat-value ${techEffects <= 0 ? 'positive' : 'negative'}`}>
            {formatPercent(techEffects ? (1/(1+(techEffects * -1)))-1 : 0)}
          </div>
          <div className="charges-stat-description">Réduction des charges non salariales</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">📝</div>
          <div className="charges-stat-title">Charges non salariales</div>
          <div className="charges-stat-value negative">
            {formatNumber(nonSalaryCharges)} or/cycle
          </div>
          <div className="charges-stat-description">Autres dépenses de la mine</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">💵</div>
          <div className="charges-stat-title">Salaires</div>
          <div className="charges-stat-value negative">
            {formatNumber(totalSalaries)} or/cycle
          </div>
          <div className="charges-stat-description">Total des salaires à payer</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">📉</div>
          <div className="charges-stat-title">Charges totales</div>
          <div className="charges-stat-value negative">
            {formatNumber(totalCharges)} or/cycle
          </div>
          <div className="charges-stat-description">Dépenses totales par cycle</div>
        </div>
      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTableAbsolute
          title="Coûts généraux"
          tableId={chargesGeneralTableId}
          modifiers={chargesGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Modificateurs généraux"
          tableId={chargesTechTableId}
          modifiers={chargesTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default ChargesTabRedux;
