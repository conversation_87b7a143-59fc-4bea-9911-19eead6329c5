.events-tab {
  padding: 20px;
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.events-header h2 {
  margin: 0;
  color: #333;
}

.refresh-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refresh-btn:hover {
  background-color: #2980b9;
}

.events-list-full {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.events-list-accordion {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 20px;
}

.event-card {
  display: flex;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.event-icon {
  font-size: 24px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
}

.event-content {
  flex: 1;
}

.event-cycle {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.event-description {
  font-size: 14px;
}

/* Styles spécifiques par type d'événement */
.event-card.sickness {
  border-left: 4px solid #e74c3c;
}

.event-card.crime {
  border-left: 4px solid #8e44ad;
}

.event-card.research {
  border-left: 4px solid #3498db;
}

.event-card.famine {
  border-left: 4px solid #f39c12;
}

.event-card.treasury {
  border-left: 4px solid #2ecc71;
}

/* Styles spécifiques par type d'événement pour l'accordéon */
.event-accordion-item.sickness {
  border-left: 4px solid #e74c3c;
}

.event-accordion-item.crime {
  border-left: 4px solid #8e44ad;
}

.event-accordion-item.research {
  border-left: 4px solid #3498db;
}

.event-accordion-item.famine {
  border-left: 4px solid #f39c12;
}

.event-accordion-item.treasury {
  border-left: 4px solid #2ecc71;
}

.event-accordion-item.building {
  border-left: 4px solid #95a5a6;
}

.event-accordion-item.population {
  border-left: 4px solid #e67e22;
}

.event-accordion-item.moral {
  border-left: 4px solid #9b59b6;
}

.event-accordion-item.default {
  border-left: 4px solid #bdc3c7;
}

/* Accordion styles */
.event-accordion-item {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.event-accordion-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.event-accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.event-accordion-header:hover {
  background-color: #f8f9fa;
}

.event-summary {
  display: flex;
  align-items: center;
  flex: 1;
}

.event-basic-info {
  margin-left: 15px;
  flex: 1;
}

.event-preview {
  font-size: 14px;
  color: #555;
  margin-top: 2px;
}

.event-expand-icon {
  font-size: 16px;
  color: #666;
  margin-left: 10px;
  transition: transform 0.2s;
}

.event-accordion-content {
  padding: 0 15px 15px 15px;
  border-top: 1px solid #eee;
  background-color: #fafafa;
}

.event-full-description {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 10px;
  color: #333;
}

.event-metadata {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #666;
}

.event-type {
  font-weight: 500;
}

.event-date {
  font-style: italic;
}

.no-events {
  text-align: center;
  padding: 40px;
  color: #666;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-events p {
  margin: 10px 0;
}

.no-events p:first-child {
  font-weight: 500;
  font-size: 16px;
}

.loading, .error {
  text-align: center;
  padding: 30px;
  color: #666;
}

.error {
  background-color: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  color: #c53030;
}

.error button {
  margin-top: 10px;
  background-color: #e53e3e;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.error button:hover {
  background-color: #c53030;
}
