/**
 * Enhanced WebSocket middleware that integrates with RTK Query
 * Provides automatic cache invalidation and real-time updates
 */

import { gameA<PERSON> } from '../store/api/gameApi';

// WebSocket action types
export const WS_CONNECT = 'WS_CONNECT';
export const WS_CONNECTED = 'WS_CONNECTED';
export const WS_DISCONNECT = 'WS_DISCONNECT';
export const WS_DISCONNECTED = 'WS_DISCONNECTED';
export const WS_MESSAGE = 'WS_MESSAGE';
export const WS_SEND = 'WS_SEND';
export const WS_ERROR = 'WS_ERROR';

// Action creators
export const wsConnect = (url) => ({ type: WS_CONNECT, payload: { url } });
export const wsDisconnect = () => ({ type: WS_DISCONNECT });
export const wsSend = (message) => ({ type: WS_SEND, payload: message });

// WebSocket middleware with RTK Query integration
const websocketRTKMiddleware = () => {
  let socket = null;
  let isConnecting = false;
  let reconnectAttempts = 0;
  let reconnectTimeoutId = null;
  let connectionTimeoutId = null;
  let lastConnectionAttempt = 0;

  // Configuration
  const MAX_RECONNECT_ATTEMPTS = 10;
  const RECONNECT_DELAY = 3000;
  const CONNECTION_TIMEOUT = 10000;
  const CONNECTION_COOLDOWN = 1000;

  const connect = (store, wsUrl) => {
    // Prevent multiple connection attempts
    const now = Date.now();
    if (now - lastConnectionAttempt < CONNECTION_COOLDOWN) {
      console.log('WebSocket: Connection attempt too soon, waiting...');
      return;
    }

    lastConnectionAttempt = now;

    if (isConnecting) {
      console.log('WebSocket: Already connecting');
      return;
    }

    if (socket !== null && socket.readyState !== WebSocket.CLOSED) {
      console.log('WebSocket: Already connected or connecting');
      return;
    }

    console.log('WebSocket: Attempting to connect to', wsUrl);
    isConnecting = true;

    // Clear any existing timeouts
    clearTimeout(reconnectTimeoutId);
    clearTimeout(connectionTimeoutId);

    // Set connection timeout
    connectionTimeoutId = setTimeout(() => {
      if (isConnecting) {
        console.error('WebSocket: Connection timeout');
        isConnecting = false;
        store.dispatch({
          type: WS_ERROR,
          payload: { message: 'Connection timeout', code: 'TIMEOUT' }
        });

        // Attempt to reconnect
        scheduleReconnect(store, wsUrl);
      }
    }, CONNECTION_TIMEOUT);

    try {
      socket = new WebSocket(wsUrl);

      socket.onopen = () => {
        console.log('WebSocket: Connected successfully');
        clearTimeout(connectionTimeoutId);
        isConnecting = false;
        reconnectAttempts = 0;

        store.dispatch({ type: WS_CONNECTED });

        // Send initial ping
        try {
          socket.send(JSON.stringify({ type: 'PING', timestamp: Date.now() }));
        } catch (err) {
          console.error('WebSocket: Error sending initial ping', err);
        }
      };

      socket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('WebSocket: Message received', message);

          // Dispatch the message to the store
          store.dispatch({
            type: WS_MESSAGE,
            payload: message
          });

          // Handle specific message types for RTK Query cache invalidation
          handleServerMessage(store, message);
        } catch (error) {
          console.error('WebSocket: Error parsing message', error);
        }
      };

      socket.onclose = (event) => {
        console.log('WebSocket: Connection closed', event.code, event.reason);
        clearTimeout(connectionTimeoutId);
        isConnecting = false;
        socket = null;

        store.dispatch({
          type: WS_DISCONNECTED,
          payload: { code: event.code, reason: event.reason }
        });

        // Attempt to reconnect unless it was a manual disconnect
        if (event.code !== 1000) {
          scheduleReconnect(store, wsUrl);
        }
      };

      socket.onerror = (error) => {
        console.error('WebSocket: Error occurred', error);
        clearTimeout(connectionTimeoutId);
        isConnecting = false;

        store.dispatch({
          type: WS_ERROR,
          payload: {
            message: 'WebSocket error',
            error: {
              type: error.type || 'error',
              message: error.message || 'WebSocket connection error',
              timestamp: Date.now()
            }
          }
        });
      };

    } catch (error) {
      console.error('WebSocket: Failed to create connection', error);
      isConnecting = false;
      clearTimeout(connectionTimeoutId);

      store.dispatch({
        type: WS_ERROR,
        payload: {
          message: 'Failed to create WebSocket connection',
          error: {
            type: 'connection_error',
            message: error.message || 'Failed to create WebSocket connection',
            timestamp: Date.now()
          }
        }
      });

      scheduleReconnect(store, wsUrl);
    }
  };

  const scheduleReconnect = (store, wsUrl) => {
    if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
      console.error('WebSocket: Max reconnection attempts reached');
      store.dispatch({
        type: WS_ERROR,
        payload: { message: 'Max reconnection attempts reached', code: 'MAX_ATTEMPTS' }
      });
      return;
    }

    reconnectAttempts++;
    const delay = RECONNECT_DELAY * Math.pow(1.5, reconnectAttempts - 1);

    console.log(`WebSocket: Scheduling reconnection attempt ${reconnectAttempts} in ${delay}ms`);

    reconnectTimeoutId = setTimeout(() => {
      connect(store, wsUrl);
    }, delay);
  };

  const handleServerMessage = (store, message) => {
    const { dispatch } = store;

    switch (message.type) {
      case 'SERVER_UPDATE':
        // Invalidate all relevant caches when server sends updates
        dispatch(gameApi.util.invalidateTags(['GameState', 'Jobs', 'Stats', 'Dashboard']));
        break;

      case 'GAME_STATE_CHANGED':
        // Invalidate game state cache
        dispatch(gameApi.util.invalidateTags(['GameState', 'Dashboard']));
        break;

      case 'JOBS_CHANGED':
        // Invalidate jobs cache
        dispatch(gameApi.util.invalidateTags(['Jobs', 'Stats', 'Dashboard']));
        break;

      case 'MODIFIERS_CHANGED':
        // Invalidate modifiers and stats cache
        dispatch(gameApi.util.invalidateTags(['Modifiers', 'Stats', 'Dashboard']));
        break;

      case 'INHABITANTS_CHANGED':
        // Invalidate inhabitants cache
        dispatch(gameApi.util.invalidateTags(['Inhabitants', 'Stats', 'Dashboard']));
        break;

      case 'BUILDINGS_CHANGED':
        // Invalidate buildings cache
        dispatch(gameApi.util.invalidateTags(['Buildings', 'Stats', 'Dashboard']));
        break;

      case 'CYCLE_PROCESSED':
        // Invalidate all caches when a cycle is processed
        dispatch(gameApi.util.invalidateTags([
          'GameState',
          'Jobs',
          'Events',
          'Stats',
          'Dashboard',
          'Modifiers'
        ]));
        break;

      case 'PONG':
        // Handle ping response
        console.log('WebSocket: Pong received, connection active');
        break;

      default:
        // For unknown message types, invalidate dashboard to be safe
        dispatch(gameApi.util.invalidateTags(['Dashboard']));
        break;
    }
  };

  const disconnect = () => {
    clearTimeout(reconnectTimeoutId);
    clearTimeout(connectionTimeoutId);
    reconnectAttempts = 0;
    isConnecting = false;

    if (socket !== null) {
      socket.close(1000, 'Manual disconnect');
      socket = null;
    }
  };

  return store => next => action => {
    switch (action.type) {
      case WS_CONNECT:
        connect(store, action.payload.url);
        break;

      case WS_DISCONNECT:
        disconnect();
        break;

      case WS_SEND:
        if (socket !== null && socket.readyState === WebSocket.OPEN) {
          const message = {
            ...action.payload,
            id: Date.now().toString(36) + Math.random().toString(36).substr(2, 5),
            timestamp: Date.now()
          };
          socket.send(JSON.stringify(message));
        } else {
          console.warn('WebSocket: Cannot send message, socket is not connected');
        }
        break;

      default:
        // Check if this action should be sent to the server
        if (action.meta && action.meta.sendToServer && socket && socket.readyState === WebSocket.OPEN) {
          const serverAction = {
            ...action,
            meta: {
              ...action.meta,
              sendToServer: undefined,
              id: Date.now().toString(36) + Math.random().toString(36).substr(2, 5),
              timestamp: Date.now()
            }
          };
          socket.send(JSON.stringify(serverAction));
        }
        break;
    }

    return next(action);
  };
};

export default websocketRTKMiddleware;
