import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetModifiersByTableQuery
} from '../store/api/gameApi';

/**
 * Custom hook for health data using RTK Query for real-time data
 */
const useHealthData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });



  // Table IDs for health modifiers
  const healthGeneralTableId = 11; // General health effects
  const healthTechTableId = 12;    // Technology effects on health

  // Get modifier tables for health
  const {
    data: healthGeneralModifiers,
    error: healthGeneralError,
    isLoading: healthGeneralLoading,
    refetch: refetchHealthGeneral
  } = useGetModifiersByTableQuery(healthGeneralTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: healthTechModifiers,
    error: healthTechError,
    isLoading: healthTechLoading,
    refetch: refetchHealthTech
  } = useGetModifiersByTableQuery(healthTechTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Connect to WebSocket on hook mount
  useEffect(() => {
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    dispatch(wsConnect(wsUrl));
  }, [dispatch]);

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get doctor information
  const jobs = gameState?.jobs || [];
  const doctors = Array.isArray(jobs) ? jobs.find(j => j.name === 'Doctor') : null;
  const activeDoctors = (doctors?.number || 0) - (doctors?.sick || 0);

  // Calculate health data from game state (all data is already calculated by the server)
  const calculateHealthData = () => {
    if (!gameState) {
      return {
        sicknessProbability: 0,
        baseSicknessProbability: 0,
        doctorEffect: 0,
        generalEffects: 0,
        techEffects: 0,
        totalSick: 0,
        sickByJob: []
      };
    }

    // Use server-calculated values from gameState (already calculated in /api/game/state)
    return {
      sicknessProbability: gameState.sickness_probability || 0,
      baseSicknessProbability: gameState.base_sickness_probability || 0,
      doctorEffect: gameState.doctor_effect || 0,
      generalEffects: gameState.health_general_modifier || 0,
      techEffects: gameState.health_tech_modifier || 0,
      totalSick: gameState.total_sick || 0,
      sickByJob: gameState.sick_by_job || []
    };
  };

  const healthData = calculateHealthData();

  // Get moral data from game state
  const moralTitle = gameState?.moral_title || 'Neutre';
  const moralModifier = gameState?.moral_modifier || 0;

  // Function to get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐';
  };

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchHealthGeneral();
    refetchHealthTech();
  };

  // Check for loading state
  const isLoading = gameStateLoading ||
                   healthGeneralLoading || healthTechLoading;

  // Check for errors
  const error = gameStateError ||
               healthGeneralError || healthTechError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      healthData: null,
      gameState: null,
      jobs: [],
      doctors: null,
      activeDoctors: 0,
      healthGeneralModifiers: [],
      healthTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      healthData: null,
      gameState: null,
      jobs: [],
      doctors: null,
      activeDoctors: 0,
      healthGeneralModifiers: [],
      healthTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    healthData,
    gameState,
    jobs,
    doctors,
    activeDoctors,
    healthGeneralModifiers: healthGeneralModifiers || [],
    healthTechModifiers: healthTechModifiers || [],
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    // Table IDs for ModifiersTable components
    healthGeneralTableId,
    healthTechTableId
  };
};

export default useHealthData;
