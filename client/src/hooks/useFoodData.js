import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetFoodStatsQuery,
  useGetModifiersByTableQuery
} from '../store/api/gameApi';
import { wsConnect } from '../middleware/websocketRTKMiddleware';

/**
 * Custom hook for food data using RTK Query for real-time data
 */
const useFoodData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: foodStats,
    error: foodStatsError,
    isLoading: foodStatsLoading,
    refetch: refetchFoodStats
  } = useGetFoodStatsQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Table IDs for food modifiers
  const foodGeneralTableId = 1; // General food effects
  const foodTechTableId = 2;    // Technology effects on food
  const perishableTableId = 3;  // Perishable rate

  // Get modifier tables for food
  const {
    data: foodGeneralModifiers,
    error: foodGeneralError,
    isLoading: foodGeneralLoading,
    refetch: refetchFoodGeneral
  } = useGetModifiersByTableQuery(foodGeneralTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: foodTechModifiers,
    error: foodTechError,
    isLoading: foodTechLoading,
    refetch: refetchFoodTech
  } = useGetModifiersByTableQuery(foodTechTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: perishableModifiers,
    error: perishableError,
    isLoading: perishableLoading,
    refetch: refetchPerishable
  } = useGetModifiersByTableQuery(perishableTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Connect to WebSocket on hook mount
  useEffect(() => {
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    dispatch(wsConnect(wsUrl));
  }, [dispatch]);

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get farmer information
  const jobs = gameState?.jobs || [];
  const farmers = Array.isArray(jobs) ? jobs.find(j => j.name === 'Farmer') : null;
  const effectiveFarmers = (farmers?.number || 0) - (farmers?.sick || 0);

  // Constants
  const FOOD_PER_FARMER = 4;

  // Calculate food data from RTK Query results
  const calculateFoodData = () => {
    if (!gameState || !foodStats) {
      return {
        currentReserves: 0,
        production: 0,
        consumption: 0,
        net: 0,
        perishableLoss: 0,
        netAfterPerishable: 0,
        seasonFactor: 1,
        perishableRate: 0,
        generalEffects: 0,
        techEffects: 0
      };
    }

    // Use server-calculated values from foodStats
    return {
      currentReserves: gameState.food_reserves || 0,
      production: foodStats.production || 0,
      consumption: foodStats.consumption || 0,
      net: foodStats.net || 0,
      perishableLoss: foodStats.perishableLoss || 0,
      netAfterPerishable: foodStats.netAfterPerishable || 0,
      seasonFactor: foodStats.nextSeasonFactor || gameState.season_factor || 1,
      perishableRate: foodStats.modifiers?.perishableRate || 0,
      generalEffects: foodStats.modifiers?.generalEffects || 0,
      techEffects: foodStats.modifiers?.techEffects || 0
    };
  };

  const foodData = calculateFoodData();

  // Get moral data from game state
  const moralTitle = gameState?.moral_title || 'Neutre';
  const moralModifier = gameState?.moral_modifier || 0;

  // Function to get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐';
  };

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchFoodStats();
    refetchFoodGeneral();
    refetchFoodTech();
    refetchPerishable();
  };

  // Check for loading state
  const isLoading = gameStateLoading || foodStatsLoading || 
                   foodGeneralLoading || foodTechLoading || perishableLoading;

  // Check for errors
  const error = gameStateError || foodStatsError || 
               foodGeneralError || foodTechError || perishableError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      foodData: null,
      gameState: null,
      jobs: [],
      farmers: null,
      effectiveFarmers: 0,
      foodGeneralModifiers: [],
      foodTechModifiers: [],
      perishableModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      FOOD_PER_FARMER
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      foodData: null,
      gameState: null,
      jobs: [],
      farmers: null,
      effectiveFarmers: 0,
      foodGeneralModifiers: [],
      foodTechModifiers: [],
      perishableModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      FOOD_PER_FARMER
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    foodData,
    gameState,
    jobs,
    farmers,
    effectiveFarmers,
    foodGeneralModifiers: foodGeneralModifiers || [],
    foodTechModifiers: foodTechModifiers || [],
    perishableModifiers: perishableModifiers || [],
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    FOOD_PER_FARMER,
    // Table IDs for ModifiersTable components
    foodGeneralTableId,
    foodTechTableId,
    perishableTableId
  };
};

export default useFoodData;
