import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetModifiersByTableQuery
} from '../store/api/gameApi';

/**
 * Custom hook for renown data using RTK Query for real-time data
 */
const useRenownData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });



  // Table IDs for renown modifiers
  const renownGeneralTableId = 13; // General renown effects

  // Get modifier tables for renown
  const {
    data: renownGeneralModifiers,
    error: renownGeneralError,
    isLoading: renownGeneralLoading,
    refetch: refetchRenownGeneral
  } = useGetModifiersByTableQuery(renownGeneralTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Connect to WebSocket on hook mount (temporarily disabled)
  // useEffect(() => {
  //   const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
  //   dispatch(wsConnect(wsUrl));
  // }, [dispatch]);

  // Format number as absolute value (not percentage) for renown
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0';
    return `${Math.round(value)}`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get job information
  const jobs = gameState?.jobs || [];

  // Calculate renown data from game state (all data is already calculated by the server)
  const calculateRenownData = () => {
    if (!gameState) {
      return {
        currentRenown: 0,
        renownChange: 0,
        generalEffects: 0,
        techEffects: 0,
        totalBonus: 0
      };
    }

    // Use server-calculated values from gameState (already calculated in /api/game/state)
    return {
      currentRenown: gameState.renown || 0,
      renownChange: gameState.renown_change || 0,
      generalEffects: gameState.renown_general_modifier || 0,
      techEffects: gameState.renown_tech_modifier || 0,
      totalBonus: gameState.renown_total_bonus || 0
    };
  };

  const renownData = calculateRenownData();

  // Get moral data from game state
  const moralTitle = gameState?.moral_title || 'Neutre';
  const moralModifier = gameState?.moral_modifier || 0;

  // Function to get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐';
  };

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchRenownGeneral();
  };

  // Check for loading state
  const isLoading = gameStateLoading || renownGeneralLoading;

  // Check for errors
  const error = gameStateError || renownGeneralError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      renownData: null,
      gameState: null,
      jobs: [],
      renownGeneralModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      renownData: null,
      gameState: null,
      jobs: [],
      renownGeneralModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    renownData,
    gameState,
    jobs,
    renownGeneralModifiers: renownGeneralModifiers?.modifiers || [],
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    // Table IDs for ModifiersTable components
    renownGeneralTableId
  };
};

export default useRenownData;
