import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetModifiersByTableQuery
} from '../store/api/gameApi';

/**
 * Custom hook for moral data using RTK Query for real-time data
 */
const useMoralData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Table ID for moral modifiers
  const moralTableId = 10; // General moral effects - Table ID 10 selon le fichier de base prompt

  // Get modifier table for moral
  const {
    data: moralModifiers,
    error: moralError,
    isLoading: moralLoading,
    refetch: refetchMoral
  } = useGetModifiersByTableQuery(moralTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get job information
  const jobs = gameState?.jobs || [];

  // Calculate moral data from game state (all data is already calculated by the server)
  const calculateMoralData = () => {
    if (!gameState) {
      return {
        currentValue: 1000,
        title: 'Neutre',
        modifier: 0,
        effectiveModifier: 0,
        housingEffect: 0,
        populationEffect: 0,
        dwellingsAvailable: 0,
        totalInhabitants: 0,
        productivityPercentage: '0.0'
      };
    }

    // Use server-calculated values from gameState (already calculated in /api/game/state)
    return {
      currentValue: gameState.moral_global_value || 1000,
      title: gameState.moral_title || 'Neutre',
      modifier: gameState.moral_modifier || 0,
      effectiveModifier: gameState.moral_value || 0,
      housingEffect: gameState.housing_effect || 0,
      populationEffect: gameState.population_effect || 0,
      dwellingsAvailable: gameState.dwellings_available || 0,
      totalInhabitants: gameState.total_inhabitants || 0,
      productivityPercentage: gameState.productivity_percentage || '0.0'
    };
  };

  const moralData = calculateMoralData();

  // Get moral data from game state
  const moralTitle = gameState?.moral_title || 'Neutre';
  const moralModifier = gameState?.moral_modifier || 0;

  // Function to get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐';
  };

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchMoral();
  };

  // Check for loading state
  const isLoading = gameStateLoading || moralLoading;

  // Check for errors
  const error = gameStateError || moralError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      moralData: null,
      gameState: null,
      jobs: [],
      moralModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      moralTableId
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      moralData: null,
      gameState: null,
      jobs: [],
      moralModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      moralTableId
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    moralData,
    gameState,
    jobs,
    moralModifiers: moralModifiers?.modifiers || [],
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    // Table ID for ModifiersTable components
    moralTableId
  };
};

export default useMoralData;
