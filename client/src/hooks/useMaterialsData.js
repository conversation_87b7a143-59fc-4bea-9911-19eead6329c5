import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetModifiersByTableQuery
} from '../store/api/gameApi';

/**
 * Custom hook for materials data using RTK Query for real-time data
 */
const useMaterialsData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameStateResponse,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Extract data from the API response
  const gameState = gameStateResponse?.gameState;
  const materialsDataFromAPI = gameStateResponse?.materialsData;



  // Table IDs for materials modifiers
  const materialsGeneralTableId = 7; // General materials effects
  const materialsTechTableId = 8;    // Technology effects on materials

  // Get modifier tables for materials
  const {
    data: materialsGeneralModifiers,
    error: materialsGeneralError,
    isLoading: materialsGeneralLoading,
    refetch: refetchMaterialsGeneral
  } = useGetModifiersByTableQuery(materialsGeneralTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: materialsTechModifiers,
    error: materialsTechError,
    isLoading: materialsTechLoading,
    refetch: refetchMaterialsTech
  } = useGetModifiersByTableQuery(materialsTechTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Connect to WebSocket on hook mount (temporarily disabled)
  // useEffect(() => {
  //   const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
  //   dispatch(wsConnect(wsUrl));
  // }, [dispatch]);

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get job information
  const jobs = gameState?.jobs || [];
  const workers = jobs.find(job => job.name === 'Ouvriers');
  const activeWorkers = workers ? workers.number : 0;

  // Constants
  const MATERIALS_PER_WORKER = 2; // Base materials production per worker

  // Calculate materials data from API response (all data is already calculated by the server)
  const calculateMaterialsData = () => {
    if (!gameState || !materialsDataFromAPI) {
      return {
        currentReserves: 0,
        production: 0,
        baseProduction: 0,
        generalEffects: 0,
        techEffects: 0,
        totalBonus: 0
      };
    }

    // Use server-calculated values from API response
    return {
      currentReserves: gameState.materials || 0,
      production: materialsDataFromAPI.production || 0,
      baseProduction: activeWorkers * MATERIALS_PER_WORKER,
      generalEffects: materialsGeneralModifiers?.table?.current_value || 0,
      techEffects: materialsTechModifiers?.table?.current_value || 0,
      totalBonus: (materialsGeneralModifiers?.table?.current_value || 0) + (materialsTechModifiers?.table?.current_value || 0)
    };
  };

  const materialsData = calculateMaterialsData();

  // Get moral data from game state
  const moralTitle = gameState?.moral_title || 'Neutre';
  const moralModifier = gameState?.moral_modifier || 0;

  // Function to get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐';
  };

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchMaterialsGeneral();
    refetchMaterialsTech();
  };

  // Check for loading state
  const isLoading = gameStateLoading ||
                   materialsGeneralLoading || materialsTechLoading;

  // Check for errors
  const error = gameStateError ||
               materialsGeneralError || materialsTechError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      materialsData: null,
      gameState: null,
      jobs: [],
      workers: null,
      activeWorkers: 0,
      materialsGeneralModifiers: [],
      materialsTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      MATERIALS_PER_WORKER
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      materialsData: null,
      gameState: null,
      jobs: [],
      workers: null,
      activeWorkers: 0,
      materialsGeneralModifiers: [],
      materialsTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      MATERIALS_PER_WORKER
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    materialsData,
    gameState,
    jobs,
    workers,
    activeWorkers,
    materialsGeneralModifiers: materialsGeneralModifiers?.modifiers || [],
    materialsTechModifiers: materialsTechModifiers?.modifiers || [],
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    MATERIALS_PER_WORKER,
    // Table IDs for ModifiersTable components
    materialsGeneralTableId,
    materialsTechTableId
  };
};

export default useMaterialsData;
