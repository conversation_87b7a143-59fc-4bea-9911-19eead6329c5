import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetMaterialsStatsQuery,
  useGetModifiersByTableQuery
} from '../store/api/gameApi';
import { wsConnect } from '../middleware/websocketRTKMiddleware';

/**
 * Custom hook for materials data using RTK Query for real-time data
 */
const useMaterialsData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: materialsStats,
    error: materialsStatsError,
    isLoading: materialsStatsLoading,
    refetch: refetchMaterialsStats
  } = useGetMaterialsStatsQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Table IDs for materials modifiers
  const materialsGeneralTableId = 7; // General materials effects
  const materialsTechTableId = 8;    // Technology effects on materials

  // Get modifier tables for materials
  const {
    data: materialsGeneralModifiers,
    error: materialsGeneralError,
    isLoading: materialsGeneralLoading,
    refetch: refetchMaterialsGeneral
  } = useGetModifiersByTableQuery(materialsGeneralTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: materialsTechModifiers,
    error: materialsTechError,
    isLoading: materialsTechLoading,
    refetch: refetchMaterialsTech
  } = useGetModifiersByTableQuery(materialsTechTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Connect to WebSocket on hook mount
  useEffect(() => {
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    dispatch(wsConnect(wsUrl));
  }, [dispatch]);

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get worker information
  const jobs = gameState?.jobs || [];
  const workers = Array.isArray(jobs) ? jobs.find(j => j.name === 'Worker') : null;
  const activeWorkers = (workers?.number || 0) - (workers?.sick || 0);

  // Constants
  const MATERIALS_PER_WORKER = 2; // Base materials production per worker

  // Calculate materials data from RTK Query results
  const calculateMaterialsData = () => {
    if (!gameState || !materialsStats) {
      return {
        currentReserves: 0,
        production: 0,
        baseProduction: 0,
        generalEffects: 0,
        techEffects: 0,
        totalBonus: 0
      };
    }

    // Use server-calculated values from materialsStats
    return {
      currentReserves: gameState.materials || 0,
      production: materialsStats.production || 0,
      baseProduction: materialsStats.baseProduction || 0,
      generalEffects: materialsStats.modifiers?.generalEffects || 0,
      techEffects: materialsStats.modifiers?.techEffects || 0,
      totalBonus: materialsStats.totalBonus || 0
    };
  };

  const materialsData = calculateMaterialsData();

  // Get moral data from game state
  const moralTitle = gameState?.moral_title || 'Neutre';
  const moralModifier = gameState?.moral_modifier || 0;

  // Function to get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐';
  };

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchMaterialsStats();
    refetchMaterialsGeneral();
    refetchMaterialsTech();
  };

  // Check for loading state
  const isLoading = gameStateLoading || materialsStatsLoading || 
                   materialsGeneralLoading || materialsTechLoading;

  // Check for errors
  const error = gameStateError || materialsStatsError || 
               materialsGeneralError || materialsTechError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      materialsData: null,
      gameState: null,
      jobs: [],
      workers: null,
      activeWorkers: 0,
      materialsGeneralModifiers: [],
      materialsTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      MATERIALS_PER_WORKER
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      materialsData: null,
      gameState: null,
      jobs: [],
      workers: null,
      activeWorkers: 0,
      materialsGeneralModifiers: [],
      materialsTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      MATERIALS_PER_WORKER
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    materialsData,
    gameState,
    jobs,
    workers,
    activeWorkers,
    materialsGeneralModifiers: materialsGeneralModifiers || [],
    materialsTechModifiers: materialsTechModifiers || [],
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    MATERIALS_PER_WORKER,
    // Table IDs for ModifiersTable components
    materialsGeneralTableId,
    materialsTechTableId
  };
};

export default useMaterialsData;
