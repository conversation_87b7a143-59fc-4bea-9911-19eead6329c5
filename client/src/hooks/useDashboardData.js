import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetDashboardQuery
} from '../store/api/gameApi';
import { wsConnect } from '../middleware/websocketRTKMiddleware';

/**
 * Custom hook for dashboard data using RTK Query for real-time data
 */
const useDashboardData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: dashboardStats,
    error: dashboardStatsError,
    isLoading: dashboardStatsLoading,
    refetch: refetchDashboardStats
  } = useGetDashboardQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Connect to WebSocket on hook mount
  useEffect(() => {
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    dispatch(wsConnect(wsUrl));
  }, [dispatch]);

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Calculate dashboard data from RTK Query results
  const calculateDashboardData = () => {
    if (!gameState || !dashboardStats) {
      return {
        gameState: gameState || null,
        currentTreasure: 0,
        miningRevenue: 0,
        tradingRevenue: 0,
        netFinances: 0,
        totalRevenue: 0,
        totalExpenses: 0
      };
    }

    // Use server-calculated values from dashboardStats
    return {
      gameState,
      currentTreasure: gameState.treasure || 0,
      miningRevenue: dashboardStats.miningRevenue || 0,
      tradingRevenue: dashboardStats.tradingRevenue || 0,
      netFinances: dashboardStats.netFinances || 0,
      totalRevenue: dashboardStats.totalRevenue || 0,
      totalExpenses: dashboardStats.totalExpenses || 0
    };
  };

  const dashboardData = calculateDashboardData();

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchDashboardStats();
  };

  // Check for loading state
  const isLoading = gameStateLoading || dashboardStatsLoading;

  // Check for errors
  const error = gameStateError || dashboardStatsError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      gameState: null,
      currentTreasure: 0,
      miningRevenue: 0,
      tradingRevenue: 0,
      netFinances: 0,
      totalRevenue: 0,
      totalExpenses: 0,
      formatNumber,
      refetchAll
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      gameState: null,
      currentTreasure: 0,
      miningRevenue: 0,
      tradingRevenue: 0,
      netFinances: 0,
      totalRevenue: 0,
      totalExpenses: 0,
      formatNumber,
      refetchAll
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    ...dashboardData,
    formatNumber,
    refetchAll
  };
};

export default useDashboardData;
