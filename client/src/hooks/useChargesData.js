import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetModifiersByTableQuery
} from '../store/api/gameApi';

/**
 * Custom hook for charges data using RTK Query for real-time data
 */
const useChargesData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });



  // Table IDs for charges modifiers
  const chargesGeneralTableId = 9;  // General charges effects
  const chargesTechTableId = 10;    // Technology effects on charges

  // Get modifier tables for charges
  const {
    data: chargesGeneralModifiers,
    error: chargesGeneralError,
    isLoading: chargesGeneralLoading,
    refetch: refetchChargesGeneral
  } = useGetModifiersByTableQuery(chargesGeneralTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: chargesTechModifiers,
    error: chargesTechError,
    isLoading: chargesTechLoading,
    refetch: refetchChargesTech
  } = useGetModifiersByTableQuery(chargesTechTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Connect to WebSocket on hook mount
  useEffect(() => {
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    dispatch(wsConnect(wsUrl));
  }, [dispatch]);

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get job information for salary calculations
  const jobs = gameState?.jobs || [];

  // Calculate charges data from game state (all data is already calculated by the server)
  const calculateChargesData = () => {
    if (!gameState) {
      return {
        totalSalaries: 0,
        nonSalaryCharges: 0,
        totalCharges: 0,
        salaryBreakdown: [],
        generalEffects: 0,
        techEffects: 0,
        craftsmanEffect: 0
      };
    }

    // Use server-calculated values from gameState (already calculated in /api/game/state)
    return {
      totalSalaries: gameState.total_salaries || 0,
      nonSalaryCharges: gameState.non_salary_charges || 0,
      totalCharges: gameState.total_charges || 0,
      salaryBreakdown: gameState.salary_breakdown || [],
      generalEffects: gameState.charges_general_modifier || 0,
      techEffects: gameState.charges_tech_modifier || 0,
      craftsmanEffect: gameState.craftsman_effect || 0
    };
  };

  const chargesData = calculateChargesData();

  // Get moral data from game state
  const moralTitle = gameState?.moral_title || 'Neutre';
  const moralModifier = gameState?.moral_modifier || 0;

  // Function to get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐';
  };

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchChargesGeneral();
    refetchChargesTech();
  };

  // Check for loading state
  const isLoading = gameStateLoading ||
                   chargesGeneralLoading || chargesTechLoading;

  // Check for errors
  const error = gameStateError ||
               chargesGeneralError || chargesTechError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      chargesData: null,
      gameState: null,
      jobs: [],
      chargesGeneralModifiers: [],
      chargesTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      chargesData: null,
      gameState: null,
      jobs: [],
      chargesGeneralModifiers: [],
      chargesTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    chargesData,
    gameState,
    jobs,
    chargesGeneralModifiers: chargesGeneralModifiers || [],
    chargesTechModifiers: chargesTechModifiers || [],
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    // Table IDs for ModifiersTable components
    chargesGeneralTableId,
    chargesTechTableId
  };
};

export default useChargesData;
