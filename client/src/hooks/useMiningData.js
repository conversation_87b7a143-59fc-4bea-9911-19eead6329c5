import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  useGetGameStateQuery,
  useGetMiningStatsQuery,
  useGetModifiersByTableQuery
} from '../store/api/gameApi';
import { wsConnect } from '../middleware/websocketRTKMiddleware';

/**
 * Custom hook for mining data using RTK Query for real-time data
 */
const useMiningData = () => {
  const dispatch = useDispatch();

  // RTK Query hooks for data fetching
  const {
    data: gameState,
    error: gameStateError,
    isLoading: gameStateLoading,
    refetch: refetchGameState
  } = useGetGameStateQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const {
    data: miningStats,
    error: miningStatsError,
    isLoading: miningStatsLoading,
    refetch: refetchMiningStats
  } = useGetMiningStatsQuery(undefined, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Table IDs for mining modifiers
  const miningGeneralTableId = 4; // General mining effects
  const engineeringTableId = 5;    // Engineering effects
  const miningTechTableId = 6;     // Technology effects on mining

  // Get modifier tables for mining
  const {
    data: miningGeneralModifiers,
    error: miningGeneralError,
    isLoading: miningGeneralLoading,
    refetch: refetchMiningGeneral
  } = useGetModifiersByTableQuery(miningGeneralTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: engineeringModifiers,
    error: engineeringError,
    isLoading: engineeringLoading,
    refetch: refetchEngineering
  } = useGetModifiersByTableQuery(engineeringTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  const {
    data: miningTechModifiers,
    error: miningTechError,
    isLoading: miningTechLoading,
    refetch: refetchMiningTech
  } = useGetModifiersByTableQuery(miningTechTableId, {
    pollingInterval: 30000,
    refetchOnMountOrArgChange: true,
  });

  // Connect to WebSocket on hook mount
  useEffect(() => {
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;
    dispatch(wsConnect(wsUrl));
  }, [dispatch]);

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get miner and engineer information
  const jobs = gameState?.jobs || [];
  const miners = Array.isArray(jobs) ? jobs.find(j => j.name === 'Miner') : null;
  const engineers = Array.isArray(jobs) ? jobs.find(j => j.name === 'Engineer') : null;
  const activeMiners = (miners?.number || 0) - (miners?.sick || 0);
  const activeEngineers = (engineers?.number || 0) - (engineers?.sick || 0);

  // Constants
  const MINING_VALUE = 30; // Base mining value per miner

  // Calculate mining data from RTK Query results
  const calculateMiningData = () => {
    if (!gameState || !miningStats) {
      return {
        production: 0,
        baseProduction: 0,
        revenuePerMiner: 0,
        totalBonus: 0,
        engineeringMultiplier: 0,
        generalEffects: 0,
        techEffects: 0,
        engineeringEffects: 0
      };
    }

    // Use server-calculated values from miningStats
    return {
      production: miningStats.production || 0,
      baseProduction: miningStats.baseProduction || 0,
      revenuePerMiner: miningStats.revenuePerMiner || 0,
      totalBonus: miningStats.totalBonus || 0,
      engineeringMultiplier: miningStats.engineeringMultiplier || 0,
      generalEffects: miningStats.modifiers?.generalEffects || 0,
      techEffects: miningStats.modifiers?.techEffects || 0,
      engineeringEffects: miningStats.modifiers?.engineeringEffects || 0
    };
  };

  const miningData = calculateMiningData();

  // Get moral data from game state
  const moralTitle = gameState?.moral_title || 'Neutre';
  const moralModifier = gameState?.moral_modifier || 0;

  // Function to get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    return '😐';
  };

  // Refetch all data function
  const refetchAll = () => {
    refetchGameState();
    refetchMiningStats();
    refetchMiningGeneral();
    refetchEngineering();
    refetchMiningTech();
  };

  // Check for loading state
  const isLoading = gameStateLoading || miningStatsLoading || 
                   miningGeneralLoading || engineeringLoading || miningTechLoading;

  // Check for errors
  const error = gameStateError || miningStatsError || 
               miningGeneralError || engineeringError || miningTechError;

  // Show loading state
  if (isLoading) {
    return {
      isLoading: true,
      error: null,
      miningData: null,
      gameState: null,
      jobs: [],
      miners: null,
      engineers: null,
      activeMiners: 0,
      activeEngineers: 0,
      miningGeneralModifiers: [],
      engineeringModifiers: [],
      miningTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      MINING_VALUE
    };
  }

  // Show error state
  if (error) {
    return {
      isLoading: false,
      error,
      miningData: null,
      gameState: null,
      jobs: [],
      miners: null,
      engineers: null,
      activeMiners: 0,
      activeEngineers: 0,
      miningGeneralModifiers: [],
      engineeringModifiers: [],
      miningTechModifiers: [],
      moralTitle: 'Neutre',
      moralModifier: 0,
      formatPercent,
      formatNumber,
      getMoralEmoji,
      refetchAll,
      MINING_VALUE
    };
  }

  // Return successful data
  return {
    isLoading: false,
    error: null,
    miningData,
    gameState,
    jobs,
    miners,
    engineers,
    activeMiners,
    activeEngineers,
    miningGeneralModifiers: miningGeneralModifiers || [],
    engineeringModifiers: engineeringModifiers || [],
    miningTechModifiers: miningTechModifiers || [],
    moralTitle,
    moralModifier,
    formatPercent,
    formatNumber,
    getMoralEmoji,
    refetchAll,
    MINING_VALUE,
    // Table IDs for ModifiersTable components
    miningGeneralTableId,
    engineeringTableId,
    miningTechTableId
  };
};

export default useMiningData;
